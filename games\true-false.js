// True False Game
class TrueFalseGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 12;
        this.isGameActive = false;
        this.currentQuestion = null;
        this.timeLimit = 20; // seconds per question (will be updated based on level)
        this.timer = null;
        this.correctAnswers = 0;
        this.streak = 0;
        this.maxStreak = 0;
        
        // Questions for different categories and levels
        this.questions = {
            1: [ // Easy - Basic facts
                {
                    question: "الشمس تشرق من الشرق",
                    answer: true,
                    category: "جغرافيا",
                    explanation: "الشمس تشرق دائماً من الشرق وتغرب في الغرب"
                },
                {
                    question: "السمك يعيش في الماء",
                    answer: true,
                    category: "علوم",
                    explanation: "الأسماك تحتاج للماء للتنفس والعيش"
                },
                {
                    question: "في السنة 13 شهراً",
                    answer: false,
                    category: "عامة",
                    explanation: "السنة تحتوي على 12 شهراً فقط"
                },
                {
                    question: "الفيل أكبر من النملة",
                    answer: true,
                    category: "حيوانات",
                    explanation: "الفيل من أكبر الحيوانات البرية"
                },
                {
                    question: "الثلج بارد",
                    answer: true,
                    category: "علوم",
                    explanation: "الثلج يتكون عند درجة حرارة منخفضة"
                },
                {
                    question: "القطط تطير",
                    answer: false,
                    category: "حيوانات",
                    explanation: "القطط حيوانات برية لا تستطيع الطيران"
                }
            ],
            2: [ // Medium - More complex facts
                {
                    question: "القاهرة عاصمة مصر",
                    answer: true,
                    category: "جغرافيا",
                    explanation: "القاهرة هي العاصمة الرسمية لجمهورية مصر العربية"
                },
                {
                    question: "الحوت من الأسماك",
                    answer: false,
                    category: "علوم",
                    explanation: "الحوت من الثدييات البحرية وليس من الأسماك"
                },
                {
                    question: "الأرض تدور حول الشمس",
                    answer: true,
                    category: "فلك",
                    explanation: "الأرض تدور حول الشمس في مدار ثابت"
                },
                {
                    question: "الماس أقسى من الحديد",
                    answer: true,
                    category: "علوم",
                    explanation: "الماس من أقسى المواد الطبيعية المعروفة"
                },
                {
                    question: "البطريق يعيش في الصحراء",
                    answer: false,
                    category: "حيوانات",
                    explanation: "البطريق يعيش في المناطق الباردة والقطبية"
                },
                {
                    question: "الضوء أسرع من الصوت",
                    answer: true,
                    category: "فيزياء",
                    explanation: "سرعة الضوء أكبر بكثير من سرعة الصوت"
                }
            ],
            3: [ // Hard - Advanced knowledge
                {
                    question: "القمر له جاذبية أقل من الأرض",
                    answer: true,
                    category: "فلك",
                    explanation: "جاذبية القمر تساوي حوالي سدس جاذبية الأرض"
                },
                {
                    question: "الذهب يذوب في الماء",
                    answer: false,
                    category: "كيمياء",
                    explanation: "الذهب لا يذوب في الماء العادي"
                },
                {
                    question: "النباتات تنتج الأكسجين",
                    answer: true,
                    category: "أحياء",
                    explanation: "النباتات تنتج الأكسجين من خلال عملية البناء الضوئي"
                },
                {
                    question: "الصوت ينتقل في الفراغ",
                    answer: false,
                    category: "فيزياء",
                    explanation: "الصوت يحتاج لوسط مادي للانتقال ولا ينتقل في الفراغ"
                },
                {
                    question: "الدم الأزرق موجود في بعض الحيوانات",
                    answer: true,
                    category: "أحياء",
                    explanation: "بعض الحيوانات مثل الأخطبوط لها دم أزرق"
                },
                {
                    question: "البرق أسرع من الرعد",
                    answer: true,
                    category: "فيزياء",
                    explanation: "نرى البرق قبل سماع الرعد لأن الضوء أسرع من الصوت"
                }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="true-false-container">
                <div class="game-header">
                    <div class="tf-stats">
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-question-circle"></i>
                            <span>السؤال: <span id="current-round">1</span>/<span id="total-rounds">12</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-fire"></i>
                            <span>السلسلة: <span id="current-streak">0</span></span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-balance-scale"></i> لعبة صح أم خطأ</h3>
                        <div class="game-description">
                            <p>اختبر معلوماتك العامة وسرعة تفكيرك!</p>
                        </div>
                        <div class="instruction-steps">
                            <div class="step-card">
                                <i class="fas fa-book-open"></i>
                                <h4>اقرأ السؤال</h4>
                                <p>اقرأ العبارة بعناية وتركيز</p>
                            </div>
                            <div class="step-card">
                                <i class="fas fa-brain"></i>
                                <h4>فكر جيداً</h4>
                                <p>استخدم معلوماتك لتحديد الإجابة</p>
                            </div>
                            <div class="step-card">
                                <i class="fas fa-mouse-pointer"></i>
                                <h4>اختر بسرعة</h4>
                                <p>اضغط صح أو خطأ قبل انتهاء الوقت</p>
                            </div>
                        </div>
                        <div class="category-selector">
                            <h4>اختر مستوى الصعوبة:</h4>
                            <div class="category-buttons">
                                <button class="category-btn active" data-level="1">
                                    <i class="fas fa-seedling"></i>
                                    <span>مبتدئ</span>
                                    <small>معلومات أساسية</small>
                                </button>
                                <button class="category-btn" data-level="2">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>متوسط</span>
                                    <small>معلومات متقدمة</small>
                                </button>
                                <button class="category-btn" data-level="3">
                                    <i class="fas fa-user-graduate"></i>
                                    <span>خبير</span>
                                    <small>معلومات متخصصة</small>
                                </button>
                            </div>
                        </div>
                        <button id="start-tf-game-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ الاختبار
                        </button>
                    </div>
                </div>
                
                <div id="game-area" class="tf-game-area hidden">
                    <div class="question-section">
                        <div class="question-header">
                            <div class="category-badge">
                                <span id="question-category">عامة</span>
                            </div>
                            <div class="timer-circle">
                                <div class="timer-text">
                                    <span id="time-left">15</span>
                                </div>
                                <svg class="timer-svg" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="45" class="timer-bg"></circle>
                                    <circle cx="50" cy="50" r="45" class="timer-progress" id="timer-circle"></circle>
                                </svg>
                            </div>
                        </div>
                        
                        <div class="question-display">
                            <h2 id="question-text">السؤال سيظهر هنا</h2>
                        </div>
                        
                        <div class="answer-buttons">
                            <button id="true-btn" class="answer-btn true-btn">
                                <i class="fas fa-check"></i>
                                <span>صح</span>
                            </button>
                            <button id="false-btn" class="answer-btn false-btn">
                                <i class="fas fa-times"></i>
                                <span>خطأ</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="result-panel" class="result-panel hidden">
                    <div class="result-content">
                        <div id="result-icon" class="result-icon"></div>
                        <h3 id="result-title"></h3>
                        <div class="explanation-box">
                            <h4>التفسير:</h4>
                            <p id="explanation-text"></p>
                        </div>
                        <div class="result-stats">
                            <span>النقاط المكتسبة: <span id="earned-points">0</span></span>
                        </div>
                        <button id="next-question-btn" class="next-btn">
                            <i class="fas fa-arrow-left"></i>
                            السؤال التالي
                        </button>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-medal complete-medal"></i>
                        <h2>انتهى الاختبار!</h2>
                        <div class="final-results">
                            <div class="result-card">
                                <i class="fas fa-star"></i>
                                <div class="result-info">
                                    <span class="result-value" id="final-score">0</span>
                                    <span class="result-label">النقاط الإجمالية</span>
                                </div>
                            </div>
                            <div class="result-card">
                                <i class="fas fa-check-circle"></i>
                                <div class="result-info">
                                    <span class="result-value" id="correct-count">0</span>
                                    <span class="result-label">إجابات صحيحة</span>
                                </div>
                            </div>
                            <div class="result-card">
                                <i class="fas fa-fire"></i>
                                <div class="result-info">
                                    <span class="result-value" id="best-streak">0</span>
                                    <span class="result-label">أفضل سلسلة</span>
                                </div>
                            </div>
                        </div>
                        <div class="knowledge-assessment">
                            <h4>تقييم المعرفة:</h4>
                            <div class="assessment-badge">
                                <span id="knowledge-level">ممتاز</span>
                                <p id="knowledge-message">لديك معرفة واسعة ومتنوعة!</p>
                            </div>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="harder-level-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                مستوى أصعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Category selection
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.category-btn').classList.add('active');
                this.currentLevel = parseInt(e.target.closest('.category-btn').dataset.level);
                this.updateTimeLimit();
            });
        });
        
        document.getElementById('start-tf-game-btn').addEventListener('click', () => this.startGame());
        document.getElementById('true-btn').addEventListener('click', () => this.selectAnswer(true));
        document.getElementById('false-btn').addEventListener('click', () => this.selectAnswer(false));
        document.getElementById('next-question-btn').addEventListener('click', () => this.nextQuestion());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('harder-level-btn').addEventListener('click', () => this.nextLevel());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.isGameActive && !document.getElementById('result-panel').classList.contains('hidden')) return;
            
            if (e.key === 'ArrowRight' || e.key === 'y' || e.key === 'Y') {
                this.selectAnswer(true);
            } else if (e.key === 'ArrowLeft' || e.key === 'n' || e.key === 'N') {
                this.selectAnswer(false);
            }
        });
    }
    
    updateTimeLimit() {
        this.timeLimit = this.currentLevel === 1 ? 20 : (this.currentLevel === 2 ? 15 : 12);
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('game-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.correctAnswers = 0;
        this.streak = 0;
        this.maxStreak = 0;
        this.updateStats();
        this.loadNextQuestion();
    }
    
    loadNextQuestion() {
        this.hideAllPanels();
        this.generateQuestion();
        this.startTimer();
    }
    
    generateQuestion() {
        const questions = this.questions[this.currentLevel];
        this.currentQuestion = questions[Math.floor(Math.random() * questions.length)];
        
        // Display question
        document.getElementById('question-text').textContent = this.currentQuestion.question;
        document.getElementById('question-category').textContent = this.currentQuestion.category;
        
        // Enable answer buttons
        document.getElementById('true-btn').disabled = false;
        document.getElementById('false-btn').disabled = false;
    }
    
    selectAnswer(userAnswer) {
        if (!this.isGameActive) return;
        
        this.stopTimer();
        
        // Disable buttons
        document.getElementById('true-btn').disabled = true;
        document.getElementById('false-btn').disabled = true;
        
        const isCorrect = userAnswer === this.currentQuestion.answer;
        
        // Calculate points
        let points = 0;
        if (isCorrect) {
            points = 10 + (this.currentLevel * 5);
            // Time bonus
            const timeBonus = Math.max(0, this.timeLimit - 5) * 2;
            points += timeBonus;
            // Streak bonus
            this.streak++;
            if (this.streak > 1) {
                points += this.streak * 2;
            }
            this.maxStreak = Math.max(this.maxStreak, this.streak);
            this.correctAnswers++;
        } else {
            this.streak = 0;
        }
        
        this.score += points;
        this.showResult(isCorrect, points);
        this.updateStats();
        
        // Add score to global score
        addScore(points);
    }
    
    startTimer() {
        const timeLeft = document.getElementById('time-left');
        const timerCircle = document.getElementById('timer-circle');
        
        let currentTime = this.timeLimit;
        timeLeft.textContent = currentTime;
        
        const circumference = 2 * Math.PI * 45;
        timerCircle.style.strokeDasharray = circumference;
        timerCircle.style.strokeDashoffset = 0;
        
        this.timer = setInterval(() => {
            currentTime--;
            timeLeft.textContent = currentTime;
            
            const progress = (this.timeLimit - currentTime) / this.timeLimit;
            const offset = circumference * progress;
            timerCircle.style.strokeDashoffset = offset;
            
            if (currentTime <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        this.streak = 0;
        this.selectAnswer(!this.currentQuestion.answer); // Wrong answer
    }
    
    showResult(isCorrect, points) {
        const resultPanel = document.getElementById('result-panel');
        const resultIcon = document.getElementById('result-icon');
        const resultTitle = document.getElementById('result-title');
        const explanationText = document.getElementById('explanation-text');
        const earnedPoints = document.getElementById('earned-points');
        
        document.getElementById('game-area').classList.add('hidden');
        
        // Show explanation
        explanationText.textContent = this.currentQuestion.explanation;
        
        if (isCorrect) {
            resultIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'إجابة صحيحة!';
            if (this.streak > 1) {
                resultTitle.textContent += ` (سلسلة ${this.streak})`;
            }
            playSuccessSound();
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'إجابة خاطئة';
            playErrorSound();
        }
        
        earnedPoints.textContent = points;
        resultPanel.classList.remove('hidden');
    }
    
    nextQuestion() {
        this.round++;
        
        if (this.round > this.totalRounds) {
            this.completeGame();
        } else {
            this.updateStats();
            this.loadNextQuestion();
        }
    }
    
    completeGame() {
        document.getElementById('result-panel').classList.add('hidden');
        
        // Calculate final statistics
        const accuracy = Math.round((this.correctAnswers / this.totalRounds) * 100);
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('correct-count').textContent = this.correctAnswers;
        document.getElementById('best-streak').textContent = this.maxStreak;
        
        // Knowledge assessment
        const knowledgeLevel = document.getElementById('knowledge-level');
        const knowledgeMessage = document.getElementById('knowledge-message');
        
        if (accuracy >= 90) {
            knowledgeLevel.textContent = 'عبقري';
            knowledgeLevel.style.color = '#48bb78';
            knowledgeMessage.textContent = 'معرفة استثنائية! أنت موسوعة معلومات!';
        } else if (accuracy >= 75) {
            knowledgeLevel.textContent = 'ممتاز';
            knowledgeLevel.style.color = '#ed8936';
            knowledgeMessage.textContent = 'معرفة واسعة ومتنوعة في مختلف المجالات!';
        } else if (accuracy >= 60) {
            knowledgeLevel.textContent = 'جيد';
            knowledgeLevel.style.color = '#667eea';
            knowledgeMessage.textContent = 'معرفة جيدة، استمر في القراءة والتعلم!';
        } else {
            knowledgeLevel.textContent = 'يحتاج تطوير';
            knowledgeLevel.style.color = '#f56565';
            knowledgeMessage.textContent = 'اقرأ أكثر لتوسيع دائرة معرفتك!';
        }
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.correctAnswers = 0;
        this.streak = 0;
        this.maxStreak = 0;
        this.hideAllPanels();
        this.showInstructions();
    }
    
    nextLevel() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
            this.updateTimeLimit();
        }
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('current-streak').textContent = this.streak;
    }
    
    hideAllPanels() {
        const panels = ['result-panel', 'game-complete-panel'];
        panels.forEach(panelId => {
            document.getElementById(panelId).classList.add('hidden');
        });
    }
}

// Function to load the true false game
function loadTrueFalseGame() {
    new TrueFalseGame(gameArea);
}
