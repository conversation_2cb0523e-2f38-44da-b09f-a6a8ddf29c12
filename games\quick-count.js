// Quick Count Game
class QuickCountGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 10;
        this.isGameActive = false;
        this.currentAnswer = 0;
        this.timeLimit = 3; // seconds to show items
        this.answerTimeLimit = 10; // seconds to answer
        this.timer = null;
        this.answerTimer = null;
        
        // Different item types for variety
        this.itemTypes = [
            { name: 'تفاح', emoji: '🍎', color: '#ff6b6b' },
            { name: 'نجوم', emoji: '⭐', color: '#ffd93d' },
            { name: 'قلوب', emoji: '❤️', color: '#ff6b9d' },
            { name: 'كرات', emoji: '⚽', color: '#4ecdc4' },
            { name: 'زهور', emoji: '🌸', color: '#ff9ff3' },
            { name: 'فراشات', emoji: '🦋', color: '#a8e6cf' },
            { name: 'سيارات', emoji: '🚗', color: '#74b9ff' },
            { name: 'طيور', emoji: '🐦', color: '#fd79a8' }
        ];
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="quick-count-container">
                <div class="game-header">
                    <div class="game-stats">
                        <div class="stat-box">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-box">
                            <i class="fas fa-layer-group"></i>
                            <span>الجولة: <span id="current-round">1</span>/<span id="total-rounds">10</span></span>
                        </div>
                        <div class="stat-box">
                            <i class="fas fa-gauge-high"></i>
                            <span>المستوى: <span id="current-level">1</span></span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-info-circle"></i> كيفية اللعب</h3>
                        <div class="instruction-steps">
                            <div class="step">
                                <span class="step-number">1</span>
                                <p>ستظهر مجموعة من العناصر لثوانٍ معدودة</p>
                            </div>
                            <div class="step">
                                <span class="step-number">2</span>
                                <p>احفظ عدد العناصر في ذاكرتك</p>
                            </div>
                            <div class="step">
                                <span class="step-number">3</span>
                                <p>أدخل العدد الصحيح بسرعة</p>
                            </div>
                        </div>
                        <button id="start-game-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ اللعب
                        </button>
                    </div>
                </div>
                
                <div id="game-area" class="count-game-area hidden">
                    <div id="countdown-display" class="countdown-display hidden">
                        <div class="countdown-number">3</div>
                        <p>استعد...</p>
                    </div>
                    
                    <div id="items-display" class="items-display hidden">
                        <div id="items-container" class="items-container">
                            <!-- Items will be generated here -->
                        </div>
                        <div class="display-timer">
                            <div id="display-progress" class="display-progress"></div>
                        </div>
                    </div>
                    
                    <div id="answer-panel" class="answer-panel hidden">
                        <div class="question-text">
                            <h3>كم عدد <span id="item-type-name"></span> التي رأيتها؟</h3>
                        </div>
                        <div class="answer-input-container">
                            <input type="number" id="answer-input" class="answer-input" min="0" max="50" placeholder="أدخل العدد">
                            <button id="submit-answer-btn" class="submit-btn">
                                <i class="fas fa-check"></i>
                                تأكيد
                            </button>
                        </div>
                        <div class="answer-timer">
                            <div id="answer-progress" class="answer-progress"></div>
                            <span id="answer-time-left">10</span> ثانية متبقية
                        </div>
                    </div>
                    
                    <div id="result-panel" class="result-panel hidden">
                        <div class="result-content">
                            <div id="result-icon" class="result-icon"></div>
                            <h3 id="result-title"></h3>
                            <div class="result-details">
                                <p>إجابتك: <span id="user-answer"></span></p>
                                <p>الإجابة الصحيحة: <span id="correct-answer"></span></p>
                                <p>النقاط المكتسبة: <span id="earned-points"></span></p>
                            </div>
                            <button id="next-round-btn" class="next-btn">
                                <i class="fas fa-arrow-left"></i>
                                الجولة التالية
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-trophy complete-icon"></i>
                        <h2>أحسنت! أكملت جميع الجولات</h2>
                        <div class="final-stats">
                            <div class="final-stat">
                                <i class="fas fa-star"></i>
                                <span>النقاط النهائية: <span id="final-score"></span></span>
                            </div>
                            <div class="final-stat">
                                <i class="fas fa-bullseye"></i>
                                <span>معدل الدقة: <span id="accuracy-rate"></span>%</span>
                            </div>
                            <div class="final-stat">
                                <i class="fas fa-clock"></i>
                                <span>متوسط وقت الإجابة: <span id="avg-time"></span>ث</span>
                            </div>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="next-level-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                المستوى التالي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        document.getElementById('start-game-btn').addEventListener('click', () => this.startGame());
        document.getElementById('submit-answer-btn').addEventListener('click', () => this.submitAnswer());
        document.getElementById('next-round-btn').addEventListener('click', () => this.nextRound());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('next-level-btn').addEventListener('click', () => this.nextLevel());
        
        // Enter key for answer input
        document.getElementById('answer-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.submitAnswer();
            }
        });
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('game-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.updateStats();
        this.startRound();
    }
    
    startRound() {
        this.hideAllPanels();
        this.showCountdown();
    }
    
    showCountdown() {
        const countdownDisplay = document.getElementById('countdown-display');
        const countdownNumber = countdownDisplay.querySelector('.countdown-number');
        
        countdownDisplay.classList.remove('hidden');
        
        let count = 3;
        countdownNumber.textContent = count;
        
        const countdownInterval = setInterval(() => {
            count--;
            if (count > 0) {
                countdownNumber.textContent = count;
            } else {
                clearInterval(countdownInterval);
                countdownDisplay.classList.add('hidden');
                this.showItems();
            }
        }, 1000);
    }
    
    showItems() {
        const itemsDisplay = document.getElementById('items-display');
        const itemsContainer = document.getElementById('items-container');
        const displayProgress = document.getElementById('display-progress');
        
        // Generate random items
        const itemType = this.itemTypes[Math.floor(Math.random() * this.itemTypes.length)];
        const itemCount = this.generateItemCount();
        this.currentAnswer = itemCount;
        
        // Clear container
        itemsContainer.innerHTML = '';
        itemsContainer.style.backgroundColor = itemType.color + '20';
        
        // Generate items with random positions
        for (let i = 0; i < itemCount; i++) {
            const item = document.createElement('div');
            item.className = 'count-item';
            item.textContent = itemType.emoji;
            
            // Random position
            const x = Math.random() * 80 + 10; // 10-90%
            const y = Math.random() * 80 + 10; // 10-90%
            
            item.style.left = x + '%';
            item.style.top = y + '%';
            item.style.animationDelay = (i * 0.1) + 's';
            
            itemsContainer.appendChild(item);
        }
        
        // Store item type for question
        document.getElementById('item-type-name').textContent = itemType.name;
        
        // Show items display
        itemsDisplay.classList.remove('hidden');
        
        // Start progress bar
        displayProgress.style.width = '100%';
        displayProgress.style.transition = `width ${this.timeLimit}s linear`;
        
        setTimeout(() => {
            displayProgress.style.width = '0%';
        }, 100);
        
        // Hide items after time limit
        setTimeout(() => {
            itemsDisplay.classList.add('hidden');
            this.showAnswerPanel();
        }, this.timeLimit * 1000);
    }
    
    generateItemCount() {
        // Increase difficulty with level
        const baseMin = 3;
        const baseMax = 8;
        const levelBonus = (this.currentLevel - 1) * 2;
        
        const min = baseMin + Math.floor(levelBonus / 2);
        const max = baseMax + levelBonus;
        
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    showAnswerPanel() {
        const answerPanel = document.getElementById('answer-panel');
        const answerInput = document.getElementById('answer-input');
        const answerProgress = document.getElementById('answer-progress');
        const answerTimeLeft = document.getElementById('answer-time-left');
        
        answerPanel.classList.remove('hidden');
        answerInput.value = '';
        answerInput.focus();
        
        // Start answer timer
        let timeLeft = this.answerTimeLimit;
        answerTimeLeft.textContent = timeLeft;
        answerProgress.style.width = '100%';
        answerProgress.style.transition = `width ${this.answerTimeLimit}s linear`;
        
        setTimeout(() => {
            answerProgress.style.width = '0%';
        }, 100);
        
        this.answerTimer = setInterval(() => {
            timeLeft--;
            answerTimeLeft.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(this.answerTimer);
                this.submitAnswer(true); // Auto-submit when time runs out
            }
        }, 1000);
    }
    
    submitAnswer(timeOut = false) {
        if (this.answerTimer) {
            clearInterval(this.answerTimer);
        }
        
        const userAnswer = timeOut ? 0 : parseInt(document.getElementById('answer-input').value) || 0;
        const isCorrect = userAnswer === this.currentAnswer;
        
        // Calculate points
        let points = 0;
        if (isCorrect) {
            points = 10 + (this.currentLevel * 5);
            // Bonus for quick answers
            const timeBonus = Math.max(0, this.answerTimeLimit - 5) * 2;
            points += timeBonus;
        }
        
        this.score += points;
        this.showResult(userAnswer, isCorrect, points);
    }
    
    showResult(userAnswer, isCorrect, points) {
        const resultPanel = document.getElementById('result-panel');
        const resultIcon = document.getElementById('result-icon');
        const resultTitle = document.getElementById('result-title');
        
        document.getElementById('answer-panel').classList.add('hidden');
        
        // Update result display
        document.getElementById('user-answer').textContent = userAnswer;
        document.getElementById('correct-answer').textContent = this.currentAnswer;
        document.getElementById('earned-points').textContent = points;
        
        if (isCorrect) {
            resultIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'إجابة صحيحة!';
            playSuccessSound();
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'إجابة خاطئة';
            playErrorSound();
        }
        
        resultPanel.classList.remove('hidden');
        this.updateStats();
        
        // Add score to global score
        addScore(points);
    }
    
    nextRound() {
        this.round++;
        
        if (this.round > this.totalRounds) {
            this.completeGame();
        } else {
            this.updateStats();
            this.startRound();
        }
    }
    
    completeGame() {
        document.getElementById('game-area').classList.add('hidden');
        
        // Calculate final statistics
        const accuracy = Math.round((this.score / (this.totalRounds * 10)) * 100);
        const avgTime = 5; // Placeholder for average time calculation
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('accuracy-rate').textContent = accuracy;
        document.getElementById('avg-time').textContent = avgTime;
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.hideAllPanels();
        this.showInstructions();
    }
    
    nextLevel() {
        this.currentLevel++;
        this.timeLimit = Math.max(2, this.timeLimit - 0.2); // Decrease display time
        this.answerTimeLimit = Math.max(5, this.answerTimeLimit - 0.5); // Decrease answer time
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('current-level').textContent = this.currentLevel;
    }
    
    hideAllPanels() {
        const panels = ['countdown-display', 'items-display', 'answer-panel', 'result-panel'];
        panels.forEach(panelId => {
            document.getElementById(panelId).classList.add('hidden');
        });
    }
}

// Function to load the quick count game
function loadQuickCountGame() {
    new QuickCountGame(gameArea);
}
