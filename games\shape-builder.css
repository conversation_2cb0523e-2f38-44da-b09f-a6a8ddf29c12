/* Shape Builder Game Styles */
.shape-builder-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.builder-stats {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(237, 137, 54, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #ed8936;
    border: 2px solid rgba(237, 137, 54, 0.2);
    min-width: 160px;
    justify-content: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(237, 137, 54, 0.2);
}

.stat-item i {
    font-size: 1.3rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(237, 137, 54, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.instructions-content h3 i {
    color: #ed8936;
    margin-left: 15px;
}

.game-intro {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.game-intro p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

.instruction-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.step-item {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.step-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #ed8936;
}

.step-item i {
    font-size: 2rem;
    color: #ed8936;
    margin-bottom: 10px;
}

.step-item h4 {
    color: #2d3748;
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.step-item p {
    color: #4a5568;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.difficulty-selection {
    margin: 30px 0;
}

.difficulty-selection h4 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.difficulty-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.difficulty-option {
    padding: 20px;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    min-width: 140px;
    text-align: center;
}

.difficulty-option:hover {
    border-color: #ed8936;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(237, 137, 54, 0.2);
}

.difficulty-option.active {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
    border-color: #ed8936;
}

.difficulty-option i {
    font-size: 1.8rem;
}

.difficulty-option span {
    font-size: 1.1rem;
}

.difficulty-option small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.start-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 30px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Game Area */
.builder-game-area {
    background: white;
    border-radius: 25px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(237, 137, 54, 0.1);
}

.game-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.target-section, .builder-section {
    text-align: center;
}

.target-section h3, .builder-section h3 {
    color: #2d3748;
    font-size: 1.5rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.shape-info {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
}

.shape-info h4 {
    color: #ed8936;
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.target-grid, .player-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 3px;
    width: 200px;
    height: 200px;
    margin: 0 auto 15px;
    border: 3px solid #e2e8f0;
    border-radius: 10px;
    padding: 10px;
    background: white;
}

.grid-cell {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.target-cell.filled {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    border-color: #ed8936;
}

.player-cell.filled {
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-color: #48bb78;
    animation: cellFill 0.3s ease;
}

.player-cell:hover {
    transform: scale(1.1);
    border-color: #48bb78;
}

.player-cell.hint-cell {
    animation: hintPulse 2s ease;
}

@keyframes cellFill {
    0% { transform: scale(0); }
    100% { transform: scale(1); }
}

@keyframes hintPulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(255, 215, 0, 0); }
}

.shape-hint {
    background: rgba(237, 137, 54, 0.1);
    padding: 10px 15px;
    border-radius: 10px;
    color: #ed8936;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 15px;
}

.grid-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.control-btn.clear {
    color: #f56565;
    border-color: #f56565;
}

.control-btn.clear:hover {
    background: #f56565;
    color: white;
    transform: translateY(-2px);
}

.control-btn.check {
    color: #48bb78;
    border-color: #48bb78;
}

.control-btn.check:hover {
    background: #48bb78;
    color: white;
    transform: translateY(-2px);
}

.control-btn.hint {
    color: #ed8936;
    border-color: #ed8936;
}

.control-btn.hint:hover {
    background: #ed8936;
    color: white;
    transform: translateY(-2px);
}

.timer-section {
    margin-top: 20px;
}

.timer-bar {
    width: 100%;
    height: 12px;
    background: #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timer-fill {
    height: 100%;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 6px;
    transition: width 0.1s ease;
}

/* Result Panel */
.result-panel {
    background: white;
    border-radius: 25px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(237, 137, 54, 0.1);
}

.result-content {
    max-width: 400px;
    margin: 0 auto;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.result-icon.success {
    color: #48bb78;
}

.result-icon.error {
    color: #f56565;
}

.result-icon.timeout {
    color: #ed8936;
}

.result-content h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

.result-content p {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.1rem;
    line-height: 1.5;
}

.result-stats {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    color: #ed8936;
    font-weight: 600;
    font-size: 1.1rem;
}

.next-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.next-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Game Complete Panel */
.game-complete-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(237, 137, 54, 0.1);
}

.complete-medal {
    font-size: 6rem;
    color: #ffd700;
    margin-bottom: 25px;
    animation: bounce 2s infinite;
}

.complete-content h2 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 35px;
    font-weight: 700;
}

.final-results {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.final-stat {
    background: #f7fafc;
    padding: 25px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 150px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.final-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.final-stat i {
    font-size: 2.5rem;
    color: #ed8936;
}

.stat-info {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.achievement-badge {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 35px;
}

.achievement-badge p {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.complete-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-8px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .shape-builder-container {
        padding: 15px;
    }
    
    .builder-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-steps {
        grid-template-columns: 1fr;
    }
    
    .difficulty-options {
        flex-direction: column;
        align-items: center;
    }
    
    .difficulty-option {
        width: 100%;
        max-width: 250px;
    }
    
    .game-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .target-grid, .player-grid {
        width: 180px;
        height: 180px;
    }
    
    .grid-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .control-btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
    
    .final-results {
        flex-direction: column;
        align-items: center;
    }
    
    .complete-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .target-grid, .player-grid {
        width: 150px;
        height: 150px;
        gap: 2px;
        padding: 8px;
    }
    
    .shape-info {
        padding: 15px;
    }
    
    .shape-info h4 {
        font-size: 1.1rem;
    }
    
    .grid-controls {
        gap: 10px;
    }
    
    .control-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .complete-medal {
        font-size: 4rem;
    }
    
    .complete-content h2 {
        font-size: 1.8rem;
    }
}
