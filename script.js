// Global Variables
let currentGame = null;
let gameScore = 0;
let gameLevel = 1;
let gameData = {};

// DOM Elements
const gameCards = document.querySelectorAll('.game-card');
const gameContainer = document.getElementById('game-container');
const gameArea = document.getElementById('game-area');
const backBtn = document.getElementById('back-btn');
const currentGameTitle = document.getElementById('current-game-title');
const scoreElement = document.getElementById('score');
const levelElement = document.getElementById('level');
const loadingScreen = document.getElementById('loading-screen');
const messageContainer = document.getElementById('message-container');

// Game Titles
const gameTitles = {
    'memory-match': 'تطابق الصور والذاكرة',
    'quick-count': 'العد السريع',
    'color-discovery': 'اكشف اللون الصحيح',
    'logic-puzzle': 'اللغز المنطقي المصور',
    'map-explorer': 'استكشف الخريطة',
    'shape-builder': 'اصنع الشكل الصحيح',
    'missing-word': 'الكلمة المفقودة',
    'guess-number': 'خمن الرقم',
    'true-false': 'صح أم خطأ؟',
    'math-chain': 'سلسلة العمليات الحسابية'
};

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Add click events to game cards
    gameCards.forEach(card => {
        card.addEventListener('click', function() {
            const gameType = this.getAttribute('data-game');
            startGame(gameType);
        });
    });

    // Back button event
    backBtn.addEventListener('click', returnToMainMenu);

    // Add hover sound effects (placeholder)
    gameCards.forEach(card => {
        card.addEventListener('mouseenter', playHoverSound);
    });
}

function startGame(gameType) {
    showLoading();
    
    setTimeout(() => {
        currentGame = gameType;
        currentGameTitle.textContent = gameTitles[gameType];
        resetGameStats();
        
        // Hide main menu and show game container
        document.querySelector('.main-content').classList.add('hidden');
        gameContainer.classList.remove('hidden');
        
        // Load specific game
        loadGame(gameType);
        hideLoading();
        
        showMessage('تم تحميل اللعبة بنجاح!', 'success');
    }, 1000);
}

function loadGame(gameType) {
    // Clear previous game content
    gameArea.innerHTML = '';
    
    switch(gameType) {
        case 'memory-match':
            loadMemoryMatchGame();
            break;
        case 'quick-count':
            loadQuickCountGame();
            break;
        case 'color-discovery':
            loadColorDiscoveryGame();
            break;
        case 'logic-puzzle':
            loadLogicPuzzleGame();
            break;
        case 'map-explorer':
            loadMapExplorerGame();
            break;
        case 'shape-builder':
            loadShapeBuilderGame();
            break;
        case 'missing-word':
            loadMissingWordGame();
            break;
        case 'guess-number':
            loadGuessNumberGame();
            break;
        case 'true-false':
            loadTrueFalseGame();
            break;
        case 'math-chain':
            loadMathChainGame();
            break;
        default:
            gameArea.innerHTML = '<p class="text-center">اللعبة قيد التطوير...</p>';
    }
}

function returnToMainMenu() {
    gameContainer.classList.add('hidden');
    document.querySelector('.main-content').classList.remove('hidden');
    currentGame = null;
    resetGameStats();
}

function resetGameStats() {
    gameScore = 0;
    gameLevel = 1;
    updateScoreDisplay();
}

function updateScoreDisplay() {
    scoreElement.textContent = `النقاط: ${gameScore}`;
    levelElement.textContent = `المستوى: ${gameLevel}`;
}

function addScore(points) {
    gameScore += points;
    updateScoreDisplay();
    
    // Level up every 100 points
    if (gameScore > 0 && gameScore % 100 === 0) {
        gameLevel++;
        updateScoreDisplay();
        showMessage(`تهانينا! وصلت للمستوى ${gameLevel}`, 'success');
    }
}

function showLoading() {
    loadingScreen.classList.remove('hidden');
}

function hideLoading() {
    loadingScreen.classList.add('hidden');
}

function showMessage(text, type = 'success') {
    const messageContent = messageContainer.querySelector('.message-content');
    const messageText = messageContainer.querySelector('.message-text');
    const messageIcon = messageContainer.querySelector('.message-icon');
    
    messageText.textContent = text;
    messageContent.className = `message-content ${type}`;
    
    if (type === 'success') {
        messageIcon.className = 'message-icon fas fa-check-circle';
    } else {
        messageIcon.className = 'message-icon fas fa-exclamation-circle';
    }
    
    messageContainer.classList.remove('hidden');
    
    setTimeout(() => {
        messageContainer.classList.add('hidden');
    }, 3000);
}

function playHoverSound() {
    // Placeholder for hover sound effect
    // Can be implemented with Web Audio API
}

function playSuccessSound() {
    // Placeholder for success sound effect
}

function playErrorSound() {
    // Placeholder for error sound effect
}

// Placeholder game loading functions (will be implemented in detail)
// Memory Match Game is now implemented in games/memory-match.js

// Quick Count Game is now implemented in games/quick-count.js

// Color Discovery Game is now implemented in games/color-discovery.js

// Logic Puzzle Game is now implemented in games/logic-puzzle.js

// Map Explorer Game is now implemented in games/map-explorer.js

// Shape Builder Game is now implemented in games/shape-builder.js

// Missing Word Game is now implemented in games/missing-word.js

// Guess Number Game is now implemented in games/guess-number.js

function loadTrueFalseGame() {
    gameArea.innerHTML = `
        <div class="game-placeholder">
            <h3>لعبة صح أم خطأ؟</h3>
            <p>قريباً سيتم تحميل اللعبة...</p>
            <div class="placeholder-question">2 + 2 = 4 ✅ ❌</div>
        </div>
    `;
}

function loadMathChainGame() {
    gameArea.innerHTML = `
        <div class="game-placeholder">
            <h3>لعبة سلسلة العمليات الحسابية</h3>
            <p>قريباً سيتم تحميل اللعبة...</p>
            <div class="placeholder-math">2 + 3 × 4 = ?</div>
        </div>
    `;
}

// Add CSS for placeholder content
const placeholderStyles = `
<style>
.game-placeholder {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.game-placeholder h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.game-placeholder p {
    color: #718096;
    margin-bottom: 30px;
}

.placeholder-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    max-width: 300px;
    margin: 0 auto;
}

.placeholder-card {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    font-size: 2rem;
    border: 2px dashed #e2e8f0;
}

.placeholder-counter, .placeholder-pattern, .placeholder-map, 
.placeholder-shapes, .placeholder-sentence, .placeholder-number, 
.placeholder-question, .placeholder-math {
    font-size: 1.5rem;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
    border: 2px dashed #e2e8f0;
    margin: 20px 0;
}

.placeholder-colors {
    margin: 20px 0;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', placeholderStyles);
