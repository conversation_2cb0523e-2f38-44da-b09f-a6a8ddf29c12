/* Guess Number Game Styles */
.guess-number-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.guess-stats {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(245, 101, 101, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #f56565;
    border: 2px solid rgba(245, 101, 101, 0.2);
    min-width: 160px;
    justify-content: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(245, 101, 101, 0.2);
}

.stat-item i {
    font-size: 1.3rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(245, 101, 101, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.instructions-content h3 i {
    color: #f56565;
    margin-left: 15px;
}

.game-intro {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.game-intro p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

.instruction-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.instruction-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #f56565;
}

.instruction-item i {
    font-size: 2rem;
    color: #f56565;
    min-width: 40px;
}

.instruction-text h4 {
    color: #2d3748;
    font-size: 1.1rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.instruction-text p {
    color: #4a5568;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.difficulty-selector {
    margin: 30px 0;
}

.difficulty-selector h4 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.difficulty-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.difficulty-btn {
    padding: 20px;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    min-width: 140px;
    text-align: center;
}

.difficulty-btn:hover {
    border-color: #f56565;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(245, 101, 101, 0.2);
}

.difficulty-btn.active {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
    border-color: #f56565;
}

.difficulty-btn i {
    font-size: 1.8rem;
}

.difficulty-btn span {
    font-size: 1.1rem;
}

.difficulty-btn small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.start-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 30px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(72, 187, 120, 0.4);
}

/* Game Area */
.guess-game-area {
    background: white;
    border-radius: 25px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(245, 101, 101, 0.1);
}

.game-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.guess-section {
    text-align: center;
}

.number-range h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 30px;
    font-weight: 600;
}

.number-range span {
    color: #f56565;
    font-weight: 700;
}

.guess-input-section {
    margin-bottom: 30px;
}

.input-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.guess-input {
    padding: 15px 20px;
    font-size: 1.5rem;
    border: 3px solid #e2e8f0;
    border-radius: 15px;
    text-align: center;
    width: 200px;
    transition: all 0.3s ease;
}

.guess-input:focus {
    outline: none;
    border-color: #f56565;
    box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.4);
}

.attempts-info {
    color: #4a5568;
    font-weight: 600;
    font-size: 1.1rem;
}

.attempts-info span:last-child {
    color: #f56565;
    font-weight: 700;
}

.feedback-section {
    margin-bottom: 20px;
}

.feedback-message {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-weight: 600;
    font-size: 1.1rem;
    max-width: 500px;
    margin: 0 auto;
    animation: slideIn 0.5s ease;
}

.feedback-message.close {
    background: rgba(72, 187, 120, 0.1);
    border-color: #48bb78;
    color: #2f855a;
}

.feedback-message.medium {
    background: rgba(237, 137, 54, 0.1);
    border-color: #ed8936;
    color: #9c4221;
}

.feedback-message.far {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #4c51bf;
}

.feedback-message.warning {
    background: rgba(245, 101, 101, 0.1);
    border-color: #f56565;
    color: #c53030;
}

.feedback-icon {
    font-size: 1.5rem;
}

.history-section {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
}

.history-section h4 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.guess-history {
    max-height: 300px;
    overflow-y: auto;
}

.no-guesses {
    color: #718096;
    font-style: italic;
    text-align: center;
    margin: 0;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: 8px;
    border-radius: 10px;
    font-weight: 600;
}

.history-item.correct {
    background: rgba(72, 187, 120, 0.1);
    border: 2px solid #48bb78;
    color: #2f855a;
}

.history-item.low {
    background: rgba(237, 137, 54, 0.1);
    border: 2px solid #ed8936;
    color: #9c4221;
}

.history-item.high {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid #667eea;
    color: #4c51bf;
}

.guess-number {
    font-size: 1.2rem;
    font-weight: 700;
}

.guess-result {
    font-size: 0.9rem;
}

.timer-section {
    margin-bottom: 20px;
}

.timer-bar {
    width: 100%;
    height: 12px;
    background: #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timer-fill {
    height: 100%;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 6px;
    transition: width 0.1s ease;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #4a5568;
}

.control-btn:hover {
    border-color: #f56565;
    background: rgba(245, 101, 101, 0.1);
    transform: translateY(-2px);
}

/* Result Panels */
.round-result-panel, .game-complete-panel {
    background: white;
    border-radius: 25px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(245, 101, 101, 0.1);
}

.result-content, .complete-content {
    max-width: 500px;
    margin: 0 auto;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.result-icon.success {
    color: #48bb78;
}

.result-icon.error {
    color: #f56565;
}

.result-content h3, .complete-content h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #2d3748;
    font-weight: 600;
}

.round-details {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 25px;
}

.round-details p {
    margin: 10px 0;
    font-size: 1.1rem;
    color: #4a5568;
}

.round-details span {
    font-weight: 600;
    color: #f56565;
}

.next-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.next-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.complete-medal {
    font-size: 6rem;
    color: #ffd700;
    margin-bottom: 25px;
    animation: bounce 2s infinite;
}

.final-results {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.result-stat {
    background: #f7fafc;
    padding: 25px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 150px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.result-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.result-stat i {
    font-size: 2.5rem;
    color: #f56565;
}

.stat-info {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.performance-analysis {
    background: #f7fafc;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 35px;
    border: 2px solid #e2e8f0;
}

.performance-analysis h4 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600;
}

.performance-analysis p {
    margin: 0;
    color: #4a5568;
    font-size: 1.1rem;
    line-height: 1.5;
}

.complete-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-8px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .guess-number-container {
        padding: 15px;
    }
    
    .guess-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-list {
        grid-template-columns: 1fr;
    }
    
    .difficulty-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .difficulty-btn {
        width: 100%;
        max-width: 250px;
    }
    
    .game-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .input-container {
        flex-direction: column;
        align-items: center;
    }
    
    .guess-input {
        width: 100%;
        max-width: 250px;
    }
    
    .submit-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .final-results {
        flex-direction: column;
        align-items: center;
    }
    
    .complete-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .number-range h3 {
        font-size: 1.5rem;
    }
    
    .guess-input {
        font-size: 1.3rem;
        padding: 12px 15px;
    }
    
    .history-section {
        padding: 15px;
    }
    
    .history-item {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    .complete-medal {
        font-size: 4rem;
    }
    
    .complete-content h2 {
        font-size: 1.6rem;
    }
}
