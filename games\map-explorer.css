/* Map Explorer Game Styles */
.map-explorer-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.explorer-stats {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(72, 187, 120, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #48bb78;
    border: 2px solid rgba(72, 187, 120, 0.2);
    min-width: 160px;
    justify-content: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.2);
}

.stat-item i {
    font-size: 1.3rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(72, 187, 120, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.instructions-content h3 i {
    color: #48bb78;
    margin-left: 15px;
}

.adventure-intro {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.adventure-intro p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-card {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.instruction-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #48bb78;
}

.instruction-card i {
    font-size: 2rem;
    color: #48bb78;
    margin-bottom: 10px;
}

.instruction-card h4 {
    color: #2d3748;
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.instruction-card p {
    color: #4a5568;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.level-selection {
    margin: 30px 0;
}

.level-selection h4 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.level-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.level-option {
    padding: 20px;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    min-width: 140px;
    text-align: center;
}

.level-option:hover {
    border-color: #48bb78;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.2);
}

.level-option.active {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border-color: #48bb78;
}

.level-option i {
    font-size: 1.8rem;
}

.level-option span {
    font-size: 1.1rem;
}

.level-option small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.start-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 30px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Map Area */
.map-area {
    background: white;
    border-radius: 25px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(72, 187, 120, 0.1);
}

.map-container {
    position: relative;
}

.game-map {
    position: relative;
    width: 100%;
    height: 500px;
    background: linear-gradient(135deg, #e6fffa, #b2f5ea);
    border-radius: 20px;
    border: 3px solid #48bb78;
    overflow: hidden;
    background-image: 
        radial-gradient(circle at 20% 30%, rgba(72, 187, 120, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
}

.player-avatar {
    position: absolute;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transform: translate(-50%, -50%);
    z-index: 10;
    animation: avatarPulse 2s infinite;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

@keyframes avatarPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

.map-location {
    position: absolute;
    transform: translate(-50%, -50%);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.map-location:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

.location-icon {
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: 3px solid #e2e8f0;
    transition: all 0.3s ease;
}

.location-name {
    background: rgba(255, 255, 255, 0.95);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-status {
    width: 25px;
    height: 25px;
    background: #48bb78;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    margin: 0 auto;
    box-shadow: 0 2px 6px rgba(72, 187, 120, 0.4);
}

.map-location.available .location-icon {
    border-color: #48bb78;
    animation: locationGlow 2s infinite;
}

.map-location.completed .location-icon {
    border-color: #ffd700;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.map-location.completed .location-status {
    background: #ffd700;
}

@keyframes locationGlow {
    0%, 100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); }
    50% { box-shadow: 0 4px 20px rgba(72, 187, 120, 0.6); }
}

.map-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f7fafc;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    color: #4a5568;
}

.legend-icon {
    font-size: 1.2rem;
}

/* Challenge Modal */
.challenge-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: 25px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.5s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
}

.modal-header h3 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    background: #f56565;
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4);
}

.challenge-question h4 {
    color: #2d3748;
    font-size: 1.3rem;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.4;
}

.challenge-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.challenge-option {
    padding: 15px 20px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #2d3748;
    text-align: center;
}

.challenge-option:hover {
    border-color: #48bb78;
    background: rgba(72, 187, 120, 0.1);
    transform: translateY(-2px);
}

.challenge-reward {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #744210;
    padding: 15px;
    border-radius: 15px;
    text-align: center;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

/* Result Modal */
.result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
    animation: fadeIn 0.3s ease;
}

.result-content {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.5s ease;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.result-icon.success {
    color: #ffd700;
}

.result-icon.error {
    color: #f56565;
}

.result-content h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.result-content p {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.1rem;
    line-height: 1.5;
}

.result-reward {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    color: #48bb78;
    font-weight: 600;
    font-size: 1.1rem;
}

.continue-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Level Complete Modal */
.level-complete-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1002;
    animation: fadeIn 0.3s ease;
}

.complete-content {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.5s ease;
}

.complete-trophy {
    font-size: 6rem;
    color: #ffd700;
    margin-bottom: 25px;
    animation: bounce 2s infinite;
}

.complete-content h2 {
    color: #2d3748;
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 700;
}

.level-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.level-stat {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 150px;
    border: 2px solid #e2e8f0;
}

.level-stat i {
    font-size: 2rem;
    color: #48bb78;
}

.level-stat span {
    font-weight: 600;
    color: #2d3748;
}

.level-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-8px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .map-explorer-container {
        padding: 15px;
    }
    
    .explorer-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-grid {
        grid-template-columns: 1fr;
    }
    
    .level-options {
        flex-direction: column;
        align-items: center;
    }
    
    .level-option {
        width: 100%;
        max-width: 250px;
    }
    
    .game-map {
        height: 400px;
    }
    
    .location-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .location-name {
        font-size: 0.7rem;
    }
    
    .challenge-options {
        grid-template-columns: 1fr;
    }
    
    .level-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .level-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .game-map {
        height: 300px;
    }
    
    .location-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .location-name {
        font-size: 0.6rem;
        padding: 3px 6px;
    }
    
    .player-avatar {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
    
    .modal-content {
        padding: 20px;
    }
    
    .challenge-question h4 {
        font-size: 1.1rem;
    }
    
    .map-legend {
        flex-direction: column;
        align-items: center;
    }
}
