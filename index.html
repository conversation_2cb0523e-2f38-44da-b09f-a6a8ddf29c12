<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ألعاب تنمية ذكاء الأطفال</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="games/memory-match.css">
    <link rel="stylesheet" href="games/quick-count.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="main-title">
                    <i class="fas fa-brain"></i>
                    ألعاب تنمية ذكاء الأطفال
                </h1>
                <p class="subtitle">ألعاب تفاعلية ممتعة لتطوير قدرات الأطفال من 6-12 سنة</p>
            </div>
        </header>

        <!-- Main Menu -->
        <main class="main-content">
            <div class="games-grid">
                <!-- Game 1: Memory Match -->
                <div class="game-card" data-game="memory-match">
                    <div class="game-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <h3 class="game-title">تطابق الصور والذاكرة</h3>
                    <p class="game-description">قوي ذاكرتك واكتشف الصور المتشابهة</p>
                    <div class="game-stats">
                        <span class="difficulty">سهل - متوسط - صعب</span>
                    </div>
                </div>

                <!-- Game 2: Quick Count -->
                <div class="game-card" data-game="quick-count">
                    <div class="game-icon">
                        <i class="fas fa-stopwatch"></i>
                    </div>
                    <h3 class="game-title">العد السريع</h3>
                    <p class="game-description">عد العناصر بسرعة واختبر تركيزك</p>
                    <div class="game-stats">
                        <span class="difficulty">تحدي الوقت</span>
                    </div>
                </div>

                <!-- Game 3: Color Discovery -->
                <div class="game-card" data-game="color-discovery">
                    <div class="game-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="game-title">اكشف اللون الصحيح</h3>
                    <p class="game-description">اختر اللون الصحيح من بين الألوان المختلفة</p>
                    <div class="game-stats">
                        <span class="difficulty">تحدي الألوان</span>
                    </div>
                </div>

                <!-- Game 4: Logic Puzzle -->
                <div class="game-card" data-game="logic-puzzle">
                    <div class="game-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <h3 class="game-title">اللغز المنطقي المصور</h3>
                    <p class="game-description">حل الألغاز المنطقية واكتشف النمط</p>
                    <div class="game-stats">
                        <span class="difficulty">تفكير منطقي</span>
                    </div>
                </div>

                <!-- Game 5: Map Explorer -->
                <div class="game-card" data-game="map-explorer">
                    <div class="game-icon">
                        <i class="fas fa-map"></i>
                    </div>
                    <h3 class="game-title">استكشف الخريطة</h3>
                    <p class="game-description">استكشف الخريطة وحل الألغاز المخفية</p>
                    <div class="game-stats">
                        <span class="difficulty">مغامرة تفاعلية</span>
                    </div>
                </div>

                <!-- Game 6: Shape Builder -->
                <div class="game-card" data-game="shape-builder">
                    <div class="game-icon">
                        <i class="fas fa-shapes"></i>
                    </div>
                    <h3 class="game-title">اصنع الشكل الصحيح</h3>
                    <p class="game-description">استخدم الكتل لبناء الأشكال المطلوبة</p>
                    <div class="game-stats">
                        <span class="difficulty">ذكاء مكاني</span>
                    </div>
                </div>

                <!-- Game 7: Missing Word -->
                <div class="game-card" data-game="missing-word">
                    <div class="game-icon">
                        <i class="fas fa-spell-check"></i>
                    </div>
                    <h3 class="game-title">الكلمة المفقودة</h3>
                    <p class="game-description">أكمل الجملة بالكلمة المناسبة</p>
                    <div class="game-stats">
                        <span class="difficulty">مهارات لغوية</span>
                    </div>
                </div>

                <!-- Game 8: Guess Number -->
                <div class="game-card" data-game="guess-number">
                    <div class="game-icon">
                        <i class="fas fa-question"></i>
                    </div>
                    <h3 class="game-title">خمن الرقم</h3>
                    <p class="game-description">خمن الرقم الصحيح باستخدام التلميحات</p>
                    <div class="game-stats">
                        <span class="difficulty">تحليل منطقي</span>
                    </div>
                </div>

                <!-- Game 9: True or False -->
                <div class="game-card" data-game="true-false">
                    <div class="game-icon">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <h3 class="game-title">صح أم خطأ؟</h3>
                    <p class="game-description">اتخذ قرارات سريعة ودقيقة</p>
                    <div class="game-stats">
                        <span class="difficulty">سرعة القرار</span>
                    </div>
                </div>

                <!-- Game 10: Math Chain -->
                <div class="game-card" data-game="math-chain">
                    <div class="game-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3 class="game-title">سلسلة العمليات الحسابية</h3>
                    <p class="game-description">حل العمليات الحسابية بسرعة</p>
                    <div class="game-stats">
                        <span class="difficulty">ذكاء رياضي</span>
                    </div>
                </div>
            </div>
        </main>

        <!-- Game Container (Hidden by default) -->
        <div id="game-container" class="game-container hidden">
            <div class="game-header">
                <button id="back-btn" class="back-btn">
                    <i class="fas fa-arrow-right"></i>
                    العودة للقائمة الرئيسية
                </button>
                <div class="game-info">
                    <h2 id="current-game-title"></h2>
                    <div class="score-board">
                        <span id="score">النقاط: 0</span>
                        <span id="level">المستوى: 1</span>
                    </div>
                </div>
            </div>
            <div id="game-area" class="game-area">
                <!-- Game content will be loaded here -->
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 ألعاب تنمية ذكاء الأطفال - تطوير تعليمي ممتع وآمن</p>
        </footer>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>جاري تحميل اللعبة...</p>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="message-container" class="message-container hidden">
        <div class="message-content">
            <i class="message-icon"></i>
            <p class="message-text"></p>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="games/memory-match.js"></script>
    <script src="games/quick-count.js"></script>
</body>
</html>
