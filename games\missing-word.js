// Missing Word Game
class MissingWordGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 10;
        this.isGameActive = false;
        this.currentSentence = null;
        this.timeLimit = 25; // seconds per question (will be updated based on level)
        this.timer = null;
        this.correctAnswers = 0;
        
        // Sentences with missing words for different levels
        this.sentences = {
            1: [ // Easy - Simple words
                {
                    sentence: "القطة تجلس على ___",
                    options: ["الكرسي", "السماء", "الماء", "النار"],
                    correct: 0,
                    hint: "مكان للجلوس",
                    image: "🪑"
                },
                {
                    sentence: "الشمس تشرق في ___",
                    options: ["الليل", "الصباح", "المساء", "الظهر"],
                    correct: 1,
                    hint: "بداية اليوم",
                    image: "🌅"
                },
                {
                    sentence: "نستخدم ___ للكتابة",
                    options: ["الملعقة", "القلم", "المفتاح", "الكوب"],
                    correct: 1,
                    hint: "أداة للكتابة",
                    image: "✏️"
                },
                {
                    sentence: "السمك يعيش في ___",
                    options: ["الشجرة", "السماء", "الماء", "الصحراء"],
                    correct: 2,
                    hint: "مكان مائي",
                    image: "🌊"
                }
            ],
            2: [ // Medium - More complex words
                {
                    sentence: "يستخدم الطبيب ___ لفحص المريض",
                    options: ["المطرقة", "السماعة", "المقص", "الملعقة"],
                    correct: 1,
                    hint: "أداة طبية للاستماع",
                    image: "🩺"
                },
                {
                    sentence: "نحتاج إلى ___ لإضاءة الغرفة المظلمة",
                    options: ["الماء", "النور", "الطعام", "الهواء"],
                    correct: 1,
                    hint: "ضد الظلام",
                    image: "💡"
                },
                {
                    sentence: "الطائر يطير بواسطة ___",
                    options: ["الأرجل", "الذيل", "الأجنحة", "المنقار"],
                    correct: 2,
                    hint: "جزء من الطائر للطيران",
                    image: "🦅"
                },
                {
                    sentence: "نضع الطعام في ___ للحفاظ عليه بارداً",
                    options: ["الفرن", "الثلاجة", "الخزانة", "الطاولة"],
                    correct: 1,
                    hint: "جهاز للتبريد",
                    image: "🧊"
                }
            ],
            3: [ // Hard - Complex sentences
                {
                    sentence: "العالم يستخدم ___ لدراسة النجوم والكواكب",
                    options: ["المجهر", "التلسكوب", "الكاميرا", "النظارة"],
                    correct: 1,
                    hint: "أداة لرؤية الأشياء البعيدة",
                    image: "🔭"
                },
                {
                    sentence: "تحتاج النباتات إلى ___ لتنمو وتكبر",
                    options: ["الظلام", "الضوضاء", "ضوء الشمس", "البرد"],
                    correct: 2,
                    hint: "مصدر طاقة للنباتات",
                    image: "🌱"
                },
                {
                    sentence: "يستخدم المهندس ___ لبناء المباني",
                    options: ["الطبخ", "التخطيط", "الغناء", "الرقص"],
                    correct: 1,
                    hint: "عملية تحضيرية للبناء",
                    image: "📐"
                },
                {
                    sentence: "الكتاب مصدر مهم للحصول على ___",
                    options: ["الطعام", "المعرفة", "الماء", "الهواء"],
                    correct: 1,
                    hint: "ما نتعلمه من القراءة",
                    image: "📚"
                }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="missing-word-container">
                <div class="game-header">
                    <div class="word-stats">
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-spell-check"></i>
                            <span>السؤال: <span id="current-round">1</span>/<span id="total-rounds">10</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-check-circle"></i>
                            <span>الصحيحة: <span id="correct-answers">0</span></span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-book-open"></i> لعبة الكلمة المفقودة</h3>
                        <div class="game-description">
                            <p>اقرأ الجملة بعناية واختر الكلمة المناسبة لإكمالها!</p>
                        </div>
                        <div class="instruction-cards">
                            <div class="instruction-card">
                                <i class="fas fa-eye"></i>
                                <h4>اقرأ الجملة</h4>
                                <p>افهم معنى الجملة والسياق</p>
                            </div>
                            <div class="instruction-card">
                                <i class="fas fa-lightbulb"></i>
                                <h4>فكر في المعنى</h4>
                                <p>ما الكلمة التي تكمل المعنى؟</p>
                            </div>
                            <div class="instruction-card">
                                <i class="fas fa-mouse-pointer"></i>
                                <h4>اختر الإجابة</h4>
                                <p>انقر على الكلمة الصحيحة</p>
                            </div>
                        </div>
                        <div class="level-selector">
                            <h4>اختر مستوى الصعوبة:</h4>
                            <div class="level-buttons">
                                <button class="level-btn active" data-level="1">
                                    <i class="fas fa-star"></i>
                                    <span>مبتدئ</span>
                                    <small>كلمات بسيطة</small>
                                </button>
                                <button class="level-btn" data-level="2">
                                    <i class="fas fa-star-half-alt"></i>
                                    <span>متوسط</span>
                                    <small>كلمات متقدمة</small>
                                </button>
                                <button class="level-btn" data-level="3">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>متقدم</span>
                                    <small>جمل معقدة</small>
                                </button>
                            </div>
                        </div>
                        <button id="start-word-game-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ اللعب
                        </button>
                    </div>
                </div>
                
                <div id="game-area" class="word-game-area hidden">
                    <div class="question-section">
                        <div class="question-header">
                            <div class="question-image">
                                <span id="sentence-image">📝</span>
                            </div>
                            <div class="timer-display">
                                <i class="fas fa-clock"></i>
                                <span id="time-left">20</span> ثانية
                                <div class="timer-progress">
                                    <div id="timer-bar" class="timer-bar"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="sentence-display">
                            <h3 id="sentence-text">الجملة ستظهر هنا</h3>
                        </div>
                        
                        <div class="hint-section">
                            <button id="show-hint-btn" class="hint-btn">
                                <i class="fas fa-question-circle"></i>
                                إظهار التلميح
                            </button>
                            <div id="hint-display" class="hint-display hidden">
                                <i class="fas fa-info-circle"></i>
                                <span id="hint-text">التلميح سيظهر هنا</span>
                            </div>
                        </div>
                    </div>
                    
                    <div id="options-section" class="options-section">
                        <!-- Word options will be generated here -->
                    </div>
                </div>
                
                <div id="result-panel" class="result-panel hidden">
                    <div class="result-content">
                        <div id="result-icon" class="result-icon"></div>
                        <h3 id="result-title"></h3>
                        <p id="result-message"></p>
                        <div class="correct-answer-display">
                            <span>الإجابة الصحيحة: <span id="correct-answer-text"></span></span>
                        </div>
                        <div class="result-stats">
                            <span>النقاط المكتسبة: <span id="earned-points">0</span></span>
                        </div>
                        <button id="next-question-btn" class="next-btn">
                            <i class="fas fa-arrow-left"></i>
                            السؤال التالي
                        </button>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-trophy complete-trophy"></i>
                        <h2>مبروك! أكملت جميع الأسئلة</h2>
                        <div class="final-stats">
                            <div class="stat-card">
                                <i class="fas fa-star"></i>
                                <div class="stat-details">
                                    <span class="stat-number" id="final-score">0</span>
                                    <span class="stat-label">النقاط الإجمالية</span>
                                </div>
                            </div>
                            <div class="stat-card">
                                <i class="fas fa-check-double"></i>
                                <div class="stat-details">
                                    <span class="stat-number" id="final-correct">0</span>
                                    <span class="stat-label">إجابات صحيحة</span>
                                </div>
                            </div>
                            <div class="stat-card">
                                <i class="fas fa-percentage"></i>
                                <div class="stat-details">
                                    <span class="stat-number" id="accuracy-percentage">0%</span>
                                    <span class="stat-label">نسبة النجاح</span>
                                </div>
                            </div>
                        </div>
                        <div class="language-assessment">
                            <h4>تقييم مهاراتك اللغوية:</h4>
                            <div class="assessment-result">
                                <span id="language-level">ممتاز</span>
                                <p id="assessment-message">لديك مهارات لغوية رائعة!</p>
                            </div>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="next-level-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                مستوى أصعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Level selection
        document.querySelectorAll('.level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.level-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.level-btn').classList.add('active');
                this.currentLevel = parseInt(e.target.closest('.level-btn').dataset.level);
                this.updateTimeLimit();
            });
        });
        
        document.getElementById('start-word-game-btn').addEventListener('click', () => this.startGame());
        document.getElementById('show-hint-btn').addEventListener('click', () => this.showHint());
        document.getElementById('next-question-btn').addEventListener('click', () => this.nextQuestion());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('next-level-btn').addEventListener('click', () => this.nextLevel());
    }
    
    updateTimeLimit() {
        this.timeLimit = this.currentLevel === 1 ? 25 : (this.currentLevel === 2 ? 20 : 15);
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('game-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.correctAnswers = 0;
        this.updateStats();
        this.loadNextQuestion();
    }
    
    loadNextQuestion() {
        this.hideAllPanels();
        this.generateQuestion();
        this.startTimer();
    }
    
    generateQuestion() {
        const sentences = this.sentences[this.currentLevel];
        this.currentSentence = sentences[Math.floor(Math.random() * sentences.length)];
        
        // Display sentence and image
        document.getElementById('sentence-text').textContent = this.currentSentence.sentence;
        document.getElementById('sentence-image').textContent = this.currentSentence.image;
        document.getElementById('hint-text').textContent = this.currentSentence.hint;
        
        // Hide hint initially
        document.getElementById('hint-display').classList.add('hidden');
        
        this.generateOptions();
    }
    
    generateOptions() {
        const optionsSection = document.getElementById('options-section');
        optionsSection.innerHTML = '';
        
        this.currentSentence.options.forEach((option, index) => {
            const optionBtn = document.createElement('button');
            optionBtn.className = 'word-option';
            optionBtn.textContent = option;
            optionBtn.addEventListener('click', () => this.selectAnswer(index));
            optionsSection.appendChild(optionBtn);
        });
    }
    
    selectAnswer(selectedIndex) {
        if (!this.isGameActive) return;
        
        this.stopTimer();
        const isCorrect = selectedIndex === this.currentSentence.correct;
        
        // Calculate points
        let points = 0;
        if (isCorrect) {
            points = 15 + (this.currentLevel * 5);
            // Time bonus
            const timeBonus = Math.max(0, this.timeLimit - 5) * 2;
            points += timeBonus;
            this.correctAnswers++;
        }
        
        this.score += points;
        this.showResult(isCorrect, points);
        this.updateStats();
        
        // Add score to global score
        addScore(points);
    }
    
    showHint() {
        const hintDisplay = document.getElementById('hint-display');
        hintDisplay.classList.remove('hidden');
        
        // Deduct points for using hint
        this.score = Math.max(0, this.score - 3);
        this.updateStats();
    }
    
    startTimer() {
        const timeLeft = document.getElementById('time-left');
        const timerBar = document.getElementById('timer-bar');
        
        let currentTime = this.timeLimit;
        timeLeft.textContent = currentTime;
        timerBar.style.width = '100%';
        timerBar.style.transition = `width ${this.timeLimit}s linear`;
        
        setTimeout(() => {
            timerBar.style.width = '0%';
        }, 100);
        
        this.timer = setInterval(() => {
            currentTime--;
            timeLeft.textContent = currentTime;
            
            if (currentTime <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        this.showResult(false, 0, true);
    }
    
    showResult(isCorrect, points, timeOut = false) {
        const resultPanel = document.getElementById('result-panel');
        const resultIcon = document.getElementById('result-icon');
        const resultTitle = document.getElementById('result-title');
        const resultMessage = document.getElementById('result-message');
        const correctAnswerText = document.getElementById('correct-answer-text');
        const earnedPoints = document.getElementById('earned-points');
        
        document.getElementById('game-area').classList.add('hidden');
        
        // Show correct answer
        correctAnswerText.textContent = this.currentSentence.options[this.currentSentence.correct];
        
        if (timeOut) {
            resultIcon.innerHTML = '<i class="fas fa-clock"></i>';
            resultIcon.className = 'result-icon timeout';
            resultTitle.textContent = 'انتهى الوقت!';
            resultMessage.textContent = 'حاول أن تقرأ بسرعة أكبر في المرة القادمة';
        } else if (isCorrect) {
            resultIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'إجابة صحيحة!';
            resultMessage.textContent = 'ممتاز! لديك فهم جيد للغة';
            playSuccessSound();
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'إجابة خاطئة';
            resultMessage.textContent = 'لا بأس، ستتحسن مع الممارسة';
            playErrorSound();
        }
        
        earnedPoints.textContent = points;
        resultPanel.classList.remove('hidden');
    }
    
    nextQuestion() {
        this.round++;
        
        if (this.round > this.totalRounds) {
            this.completeGame();
        } else {
            this.updateStats();
            this.loadNextQuestion();
        }
    }
    
    completeGame() {
        document.getElementById('result-panel').classList.add('hidden');
        
        // Calculate final statistics
        const accuracy = Math.round((this.correctAnswers / this.totalRounds) * 100);
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-correct').textContent = this.correctAnswers;
        document.getElementById('accuracy-percentage').textContent = accuracy;
        
        // Language assessment
        const languageLevel = document.getElementById('language-level');
        const assessmentMessage = document.getElementById('assessment-message');
        
        if (accuracy >= 90) {
            languageLevel.textContent = 'ممتاز';
            languageLevel.style.color = '#48bb78';
            assessmentMessage.textContent = 'لديك مهارات لغوية استثنائية!';
        } else if (accuracy >= 70) {
            languageLevel.textContent = 'جيد جداً';
            languageLevel.style.color = '#ed8936';
            assessmentMessage.textContent = 'مهاراتك اللغوية جيدة ومتطورة!';
        } else if (accuracy >= 50) {
            languageLevel.textContent = 'جيد';
            languageLevel.style.color = '#667eea';
            assessmentMessage.textContent = 'تحتاج لمزيد من القراءة والممارسة!';
        } else {
            languageLevel.textContent = 'يحتاج تحسين';
            languageLevel.style.color = '#f56565';
            assessmentMessage.textContent = 'اقرأ أكثر وستتحسن مهاراتك!';
        }
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.correctAnswers = 0;
        this.hideAllPanels();
        this.showInstructions();
    }
    
    nextLevel() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.level-btn').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
            this.updateTimeLimit();
        }
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('correct-answers').textContent = this.correctAnswers;
    }
    
    hideAllPanels() {
        const panels = ['result-panel', 'game-complete-panel'];
        panels.forEach(panelId => {
            document.getElementById(panelId).classList.add('hidden');
        });
    }
}

// Function to load the missing word game
function loadMissingWordGame() {
    new MissingWordGame(gameArea);
}
