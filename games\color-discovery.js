// Color Discovery Game
class ColorDiscoveryGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 15;
        this.isGameActive = false;
        this.currentCorrectColor = null;
        this.timeLimit = this.currentLevel === 1 ? 10 : (this.currentLevel === 2 ? 8 : 6); // seconds to answer
        this.timer = null;
        this.streakCount = 0;
        this.maxStreak = 0;
        
        // Color definitions
        this.colors = [
            { name: 'أحمر', value: '#e53e3e', textColor: '#ffffff' },
            { name: 'أزرق', value: '#3182ce', textColor: '#ffffff' },
            { name: 'أخضر', value: '#38a169', textColor: '#ffffff' },
            { name: 'أصفر', value: '#d69e2e', textColor: '#000000' },
            { name: 'بنفسجي', value: '#805ad5', textColor: '#ffffff' },
            { name: 'برتقالي', value: '#dd6b20', textColor: '#ffffff' },
            { name: 'وردي', value: '#d53f8c', textColor: '#ffffff' },
            { name: 'بني', value: '#8b4513', textColor: '#ffffff' },
            { name: 'رمادي', value: '#718096', textColor: '#ffffff' },
            { name: 'أسود', value: '#2d3748', textColor: '#ffffff' }
        ];
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="color-discovery-container">
                <div class="game-header">
                    <div class="game-stats">
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-layer-group"></i>
                            <span>الجولة: <span id="current-round">1</span>/<span id="total-rounds">15</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-fire"></i>
                            <span>السلسلة: <span id="current-streak">0</span></span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-palette"></i> كيفية اللعب</h3>
                        <div class="instruction-list">
                            <div class="instruction-item">
                                <i class="fas fa-eye"></i>
                                <p>انظر إلى اللون المطلوب في الأعلى</p>
                            </div>
                            <div class="instruction-item">
                                <i class="fas fa-mouse-pointer"></i>
                                <p>اختر اللون الصحيح من الخيارات</p>
                            </div>
                            <div class="instruction-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>احذر! أحياناً يكون اسم اللون مختلف عن لونه</p>
                            </div>
                            <div class="instruction-item">
                                <i class="fas fa-clock"></i>
                                <p>لديك وقت محدود للإجابة</p>
                            </div>
                        </div>
                        <div class="difficulty-selector">
                            <h4>اختر مستوى الصعوبة:</h4>
                            <div class="difficulty-buttons">
                                <button class="difficulty-btn active" data-level="1">
                                    <i class="fas fa-smile"></i>
                                    سهل
                                </button>
                                <button class="difficulty-btn" data-level="2">
                                    <i class="fas fa-meh"></i>
                                    متوسط
                                </button>
                                <button class="difficulty-btn" data-level="3">
                                    <i class="fas fa-dizzy"></i>
                                    صعب
                                </button>
                            </div>
                        </div>
                        <button id="start-game-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ اللعب
                        </button>
                    </div>
                </div>
                
                <div id="game-area" class="color-game-area hidden">
                    <div class="target-color-section">
                        <h3>اختر هذا اللون:</h3>
                        <div id="target-color-display" class="target-color-display">
                            <div id="target-color-box" class="target-color-box"></div>
                            <div id="target-color-text" class="target-color-text"></div>
                        </div>
                    </div>
                    
                    <div class="timer-section">
                        <div class="timer-bar">
                            <div id="timer-progress" class="timer-progress"></div>
                        </div>
                        <span id="time-left">8</span> ثانية متبقية
                    </div>
                    
                    <div id="color-options" class="color-options">
                        <!-- Color options will be generated here -->
                    </div>
                    
                    <div id="result-feedback" class="result-feedback hidden">
                        <div class="feedback-content">
                            <div id="feedback-icon" class="feedback-icon"></div>
                            <h4 id="feedback-title"></h4>
                            <p id="feedback-message"></p>
                            <div class="feedback-stats">
                                <span>النقاط المكتسبة: <span id="round-points">0</span></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-medal complete-icon"></i>
                        <h2>أحسنت! أكملت التحدي</h2>
                        <div class="final-stats">
                            <div class="final-stat">
                                <i class="fas fa-star"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="final-score">0</span>
                                    <span class="stat-label">النقاط النهائية</span>
                                </div>
                            </div>
                            <div class="final-stat">
                                <i class="fas fa-bullseye"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="accuracy-rate">0%</span>
                                    <span class="stat-label">معدل الدقة</span>
                                </div>
                            </div>
                            <div class="final-stat">
                                <i class="fas fa-fire"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="max-streak">0</span>
                                    <span class="stat-label">أطول سلسلة</span>
                                </div>
                            </div>
                        </div>
                        <div class="performance-message">
                            <p id="performance-text"></p>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="next-difficulty-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                مستوى أصعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Difficulty selection
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentLevel = parseInt(e.target.dataset.level);
            });
        });
        
        document.getElementById('start-game-btn').addEventListener('click', () => this.startGame());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('next-difficulty-btn').addEventListener('click', () => this.nextDifficulty());
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('game-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.streakCount = 0;
        this.maxStreak = 0;
        this.updateStats();
        this.startRound();
    }
    
    startRound() {
        this.hideResultFeedback();
        this.generateColorChallenge();
        this.startTimer();
    }
    
    generateColorChallenge() {
        // Select target color
        this.currentCorrectColor = this.colors[Math.floor(Math.random() * this.colors.length)];
        
        // Display target color
        const targetColorBox = document.getElementById('target-color-box');
        const targetColorText = document.getElementById('target-color-text');
        
        if (this.currentLevel === 1) {
            // Easy: Color box matches the color name
            targetColorBox.style.backgroundColor = this.currentCorrectColor.value;
            targetColorText.textContent = this.currentCorrectColor.name;
            targetColorText.style.color = this.currentCorrectColor.textColor;
        } else if (this.currentLevel === 2) {
            // Medium: Sometimes Stroop effect
            const useStroop = Math.random() < 0.4;
            if (useStroop) {
                const wrongColor = this.colors[Math.floor(Math.random() * this.colors.length)];
                targetColorBox.style.backgroundColor = wrongColor.value;
                targetColorText.textContent = this.currentCorrectColor.name;
                targetColorText.style.color = wrongColor.textColor;
            } else {
                targetColorBox.style.backgroundColor = this.currentCorrectColor.value;
                targetColorText.textContent = this.currentCorrectColor.name;
                targetColorText.style.color = this.currentCorrectColor.textColor;
            }
        } else {
            // Hard: Always Stroop effect
            const wrongColor = this.colors.filter(c => c !== this.currentCorrectColor)[Math.floor(Math.random() * (this.colors.length - 1))];
            targetColorBox.style.backgroundColor = wrongColor.value;
            targetColorText.textContent = this.currentCorrectColor.name;
            targetColorText.style.color = wrongColor.textColor;
        }
        
        this.generateColorOptions();
    }
    
    generateColorOptions() {
        const colorOptions = document.getElementById('color-options');
        colorOptions.innerHTML = '';
        
        // Create array of options including correct answer
        const options = [this.currentCorrectColor];
        
        // Add wrong options
        const wrongOptions = this.colors.filter(c => c !== this.currentCorrectColor);
        const numWrongOptions = this.currentLevel === 1 ? 3 : (this.currentLevel === 2 ? 5 : 7);
        
        for (let i = 0; i < numWrongOptions; i++) {
            if (wrongOptions.length > 0) {
                const randomIndex = Math.floor(Math.random() * wrongOptions.length);
                options.push(wrongOptions.splice(randomIndex, 1)[0]);
            }
        }
        
        // Shuffle options
        this.shuffleArray(options);
        
        // Create option buttons
        options.forEach(color => {
            const optionBtn = document.createElement('button');
            optionBtn.className = 'color-option';
            optionBtn.style.backgroundColor = color.value;
            optionBtn.style.color = color.textColor;
            optionBtn.textContent = color.name;
            optionBtn.addEventListener('click', () => this.selectColor(color));
            colorOptions.appendChild(optionBtn);
        });
    }
    
    selectColor(selectedColor) {
        if (!this.isGameActive) return;
        
        this.stopTimer();
        const isCorrect = selectedColor === this.currentCorrectColor;
        
        // Calculate points
        let points = 0;
        if (isCorrect) {
            points = 10 + (this.currentLevel * 5);
            // Time bonus
            const timeBonus = Math.max(0, this.timeLimit - 3) * 2;
            points += timeBonus;
            // Streak bonus
            this.streakCount++;
            if (this.streakCount > 1) {
                points += this.streakCount * 2;
            }
            this.maxStreak = Math.max(this.maxStreak, this.streakCount);
        } else {
            this.streakCount = 0;
        }
        
        this.score += points;
        this.showResultFeedback(isCorrect, points);
        this.updateStats();
        
        // Add score to global score
        addScore(points);
        
        // Continue to next round after delay
        setTimeout(() => {
            this.round++;
            if (this.round > this.totalRounds) {
                this.completeGame();
            } else {
                this.startRound();
            }
        }, 2000);
    }
    
    startTimer() {
        const timerProgress = document.getElementById('timer-progress');
        const timeLeft = document.getElementById('time-left');
        
        let currentTime = this.timeLimit;
        timeLeft.textContent = currentTime;
        timerProgress.style.width = '100%';
        timerProgress.style.transition = `width ${this.timeLimit}s linear`;
        
        setTimeout(() => {
            timerProgress.style.width = '0%';
        }, 100);
        
        this.timer = setInterval(() => {
            currentTime--;
            timeLeft.textContent = currentTime;
            
            if (currentTime <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        this.streakCount = 0;
        this.showResultFeedback(false, 0, true);
        
        setTimeout(() => {
            this.round++;
            if (this.round > this.totalRounds) {
                this.completeGame();
            } else {
                this.startRound();
            }
        }, 2000);
    }
    
    showResultFeedback(isCorrect, points, timeOut = false) {
        const resultFeedback = document.getElementById('result-feedback');
        const feedbackIcon = document.getElementById('feedback-icon');
        const feedbackTitle = document.getElementById('feedback-title');
        const feedbackMessage = document.getElementById('feedback-message');
        const roundPoints = document.getElementById('round-points');
        
        if (timeOut) {
            feedbackIcon.innerHTML = '<i class="fas fa-clock"></i>';
            feedbackIcon.className = 'feedback-icon timeout';
            feedbackTitle.textContent = 'انتهى الوقت!';
            feedbackMessage.textContent = `الإجابة الصحيحة كانت: ${this.currentCorrectColor.name}`;
        } else if (isCorrect) {
            feedbackIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            feedbackIcon.className = 'feedback-icon success';
            feedbackTitle.textContent = 'إجابة صحيحة!';
            feedbackMessage.textContent = this.streakCount > 1 ? `سلسلة رائعة! ${this.streakCount} إجابات صحيحة متتالية` : 'أحسنت!';
            playSuccessSound();
        } else {
            feedbackIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            feedbackIcon.className = 'feedback-icon error';
            feedbackTitle.textContent = 'إجابة خاطئة';
            feedbackMessage.textContent = `الإجابة الصحيحة كانت: ${this.currentCorrectColor.name}`;
            playErrorSound();
        }
        
        roundPoints.textContent = points;
        resultFeedback.classList.remove('hidden');
    }
    
    hideResultFeedback() {
        document.getElementById('result-feedback').classList.add('hidden');
    }
    
    completeGame() {
        document.getElementById('game-area').classList.add('hidden');
        
        // Calculate final statistics
        const accuracy = Math.round((this.score / (this.totalRounds * 10)) * 100);
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('accuracy-rate').textContent = accuracy;
        document.getElementById('max-streak').textContent = this.maxStreak;
        
        // Performance message
        const performanceText = document.getElementById('performance-text');
        if (accuracy >= 90) {
            performanceText.textContent = 'أداء ممتاز! لديك تركيز عالي جداً!';
        } else if (accuracy >= 70) {
            performanceText.textContent = 'أداء جيد! يمكنك تحسين تركيزك أكثر.';
        } else {
            performanceText.textContent = 'حاول مرة أخرى وركز أكثر على الألوان!';
        }
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.streakCount = 0;
        this.maxStreak = 0;
        document.getElementById('game-complete-panel').classList.add('hidden');
        this.showInstructions();
    }
    
    nextDifficulty() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
        }
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('current-streak').textContent = this.streakCount;
    }
    
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}

// Function to load the color discovery game
function loadColorDiscoveryGame() {
    new ColorDiscoveryGame(gameArea);
}
