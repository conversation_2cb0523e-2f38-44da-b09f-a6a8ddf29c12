/* Memory Match Game Styles */
.memory-game-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(102, 126, 234, 0.1);
    padding: 10px 15px;
    border-radius: 20px;
    font-weight: 600;
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.2);
}

.stat-item i {
    font-size: 1.1rem;
}

.level-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.cards-container {
    display: grid;
    gap: 15px;
    margin-bottom: 30px;
    justify-content: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.memory-card {
    width: 80px;
    height: 80px;
    perspective: 1000px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.memory-card:hover {
    transform: scale(1.05);
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
    border-radius: 12px;
}

.memory-card.flipped .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 3px solid #e2e8f0;
}

.card-front {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-size: 1.5rem;
}

.card-back {
    background: white;
    color: #2d3748;
    transform: rotateY(180deg);
    font-size: 2rem;
}

.memory-card.matched .card-inner {
    transform: rotateY(180deg);
}

.memory-card.matched .card-back {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    animation: matchPulse 0.6s ease;
}

.memory-card.hint {
    animation: hintGlow 1.5s ease;
}

@keyframes matchPulse {
    0%, 100% { transform: rotateY(180deg) scale(1); }
    50% { transform: rotateY(180deg) scale(1.1); }
}

@keyframes hintGlow {
    0%, 100% { box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}

.level-complete {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.completion-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.5s ease;
}

.completion-icon {
    font-size: 4rem;
    color: #ffd700;
    margin-bottom: 20px;
    animation: bounce 1s infinite;
}

.completion-content h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 20px;
}

.completion-stats {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.completion-stats p {
    margin: 10px 0;
    font-size: 1.1rem;
    color: #4a5568;
}

.completion-stats span {
    font-weight: 600;
    color: #667eea;
}

.completion-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .memory-card {
        width: 60px;
        height: 60px;
    }
    
    .card-back {
        font-size: 1.5rem;
    }
    
    .game-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .game-stats {
        justify-content: center;
    }
    
    .level-controls {
        justify-content: center;
    }
    
    .completion-content {
        padding: 30px 20px;
    }
    
    .completion-actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .memory-card {
        width: 50px;
        height: 50px;
    }
    
    .card-back {
        font-size: 1.2rem;
    }
    
    .cards-container {
        gap: 10px;
    }
    
    .stat-item {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
    
    .control-btn {
        font-size: 0.8rem;
        padding: 8px 12px;
    }
}

/* Special effects for different levels */
.level-2 .card-front {
    background: linear-gradient(135deg, #48bb78, #38a169);
}

.level-3 .card-front {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.level-4 .card-front {
    background: linear-gradient(135deg, #e53e3e, #c53030);
}

/* Card flip animation variations */
.memory-card:nth-child(even) .card-inner {
    transition: transform 0.8s;
}

.memory-card:nth-child(3n) .card-inner {
    transition: transform 0.7s;
}

/* Particle effect for matches */
.memory-card.matched::after {
    content: '✨';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.2rem;
    animation: sparkle 1s ease-out;
    pointer-events: none;
}

@keyframes sparkle {
    0% { transform: scale(0) rotate(0deg); opacity: 1; }
    100% { transform: scale(1.5) rotate(180deg); opacity: 0; }
}
