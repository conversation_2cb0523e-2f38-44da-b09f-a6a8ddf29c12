// Guess Number Game
class GuessNumberGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 5;
        this.isGameActive = false;
        this.secretNumber = 0;
        this.attempts = 0;
        this.maxAttempts = 7;
        this.timeLimit = 150; // will be updated based on level
        this.timer = null;
        this.guessHistory = [];
        this.roundsWon = 0;
        
        // Game configurations for different levels
        this.levelConfigs = {
            1: { min: 1, max: 50, maxAttempts: 8, timeLimit: 150 },
            2: { min: 1, max: 100, maxAttempts: 7, timeLimit: 120 },
            3: { min: 1, max: 200, maxAttempts: 6, timeLimit: 90 }
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="guess-number-container">
                <div class="game-header">
                    <div class="guess-stats">
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-bullseye"></i>
                            <span>الجولة: <span id="current-round">1</span>/<span id="total-rounds">5</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <span>الوقت: <span id="time-remaining">120</span>ث</span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-search"></i> لعبة خمن الرقم</h3>
                        <div class="game-intro">
                            <p>استخدم ذكاءك وقوة التحليل لاكتشاف الرقم السري!</p>
                        </div>
                        <div class="instruction-list">
                            <div class="instruction-item">
                                <i class="fas fa-brain"></i>
                                <div class="instruction-text">
                                    <h4>فكر بذكاء</h4>
                                    <p>استخدم التلميحات لتضييق نطاق البحث</p>
                                </div>
                            </div>
                            <div class="instruction-item">
                                <i class="fas fa-chart-line"></i>
                                <div class="instruction-text">
                                    <h4>حلل النتائج</h4>
                                    <p>كل تخمين يعطيك معلومة مفيدة</p>
                                </div>
                            </div>
                            <div class="instruction-item">
                                <i class="fas fa-target"></i>
                                <div class="instruction-text">
                                    <h4>اقترب من الهدف</h4>
                                    <p>استخدم أقل عدد من المحاولات</p>
                                </div>
                            </div>
                        </div>
                        <div class="difficulty-selector">
                            <h4>اختر مستوى التحدي:</h4>
                            <div class="difficulty-buttons">
                                <button class="difficulty-btn active" data-level="1">
                                    <i class="fas fa-seedling"></i>
                                    <span>مبتدئ</span>
                                    <small>1-50 (8 محاولات)</small>
                                </button>
                                <button class="difficulty-btn" data-level="2">
                                    <i class="fas fa-fire"></i>
                                    <span>متوسط</span>
                                    <small>1-100 (7 محاولات)</small>
                                </button>
                                <button class="difficulty-btn" data-level="3">
                                    <i class="fas fa-rocket"></i>
                                    <span>خبير</span>
                                    <small>1-200 (6 محاولات)</small>
                                </button>
                            </div>
                        </div>
                        <button id="start-guessing-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ التخمين
                        </button>
                    </div>
                </div>
                
                <div id="game-area" class="guess-game-area hidden">
                    <div class="game-layout">
                        <div class="guess-section">
                            <div class="number-range">
                                <h3>خمن الرقم بين <span id="min-number">1</span> و <span id="max-number">100</span></h3>
                            </div>
                            
                            <div class="guess-input-section">
                                <div class="input-container">
                                    <input type="number" id="guess-input" class="guess-input" placeholder="أدخل تخمينك">
                                    <button id="submit-guess-btn" class="submit-btn">
                                        <i class="fas fa-paper-plane"></i>
                                        خمن
                                    </button>
                                </div>
                                <div class="attempts-info">
                                    <span>المحاولات المتبقية: <span id="attempts-left">7</span></span>
                                </div>
                            </div>
                            
                            <div id="feedback-section" class="feedback-section">
                                <div id="feedback-message" class="feedback-message hidden">
                                    <i id="feedback-icon" class="feedback-icon"></i>
                                    <span id="feedback-text">ابدأ بتخمين رقم!</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="history-section">
                            <h4><i class="fas fa-history"></i> تاريخ التخمينات</h4>
                            <div id="guess-history" class="guess-history">
                                <p class="no-guesses">لم تبدأ التخمين بعد</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="timer-section">
                        <div class="timer-bar">
                            <div id="timer-fill" class="timer-fill"></div>
                        </div>
                    </div>
                    
                    <div class="game-controls">
                        <button id="new-number-btn" class="control-btn">
                            <i class="fas fa-redo"></i>
                            رقم جديد
                        </button>
                        <button id="hint-btn" class="control-btn">
                            <i class="fas fa-lightbulb"></i>
                            تلميح
                        </button>
                    </div>
                </div>
                
                <div id="round-result-panel" class="round-result-panel hidden">
                    <div class="result-content">
                        <div id="round-result-icon" class="result-icon"></div>
                        <h3 id="round-result-title"></h3>
                        <div class="round-details">
                            <p>الرقم السري كان: <span id="secret-number-reveal"></span></p>
                            <p>عدد المحاولات: <span id="attempts-used"></span></p>
                            <p>النقاط المكتسبة: <span id="round-points"></span></p>
                        </div>
                        <button id="next-round-btn" class="next-btn">
                            <i class="fas fa-arrow-left"></i>
                            الجولة التالية
                        </button>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-medal complete-medal"></i>
                        <h2>انتهت اللعبة!</h2>
                        <div class="final-results">
                            <div class="result-stat">
                                <i class="fas fa-star"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="final-score">0</span>
                                    <span class="stat-label">النقاط الإجمالية</span>
                                </div>
                            </div>
                            <div class="result-stat">
                                <i class="fas fa-trophy"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="rounds-won">0</span>
                                    <span class="stat-label">جولات فائزة</span>
                                </div>
                            </div>
                            <div class="result-stat">
                                <i class="fas fa-brain"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="analysis-skill">ممتاز</span>
                                    <span class="stat-label">مهارة التحليل</span>
                                </div>
                            </div>
                        </div>
                        <div class="performance-analysis">
                            <h4>تحليل الأداء:</h4>
                            <p id="performance-message">أداء رائع في التحليل المنطقي!</p>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="harder-difficulty-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                صعوبة أعلى
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Difficulty selection
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.difficulty-btn').classList.add('active');
                this.currentLevel = parseInt(e.target.closest('.difficulty-btn').dataset.level);
                this.updateGameConfig();
            });
        });
        
        document.getElementById('start-guessing-btn').addEventListener('click', () => this.startGame());
        document.getElementById('submit-guess-btn').addEventListener('click', () => this.submitGuess());
        document.getElementById('new-number-btn').addEventListener('click', () => this.generateNewNumber());
        document.getElementById('hint-btn').addEventListener('click', () => this.showHint());
        document.getElementById('next-round-btn').addEventListener('click', () => this.nextRound());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('harder-difficulty-btn').addEventListener('click', () => this.increaseDifficulty());
        
        // Enter key for guess input
        document.getElementById('guess-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.submitGuess();
            }
        });
    }
    
    updateGameConfig() {
        const config = this.levelConfigs[this.currentLevel];
        this.maxAttempts = config.maxAttempts;
        this.timeLimit = config.timeLimit;
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('game-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.roundsWon = 0;
        this.updateStats();
        this.startNewRound();
    }
    
    startNewRound() {
        this.hideAllPanels();
        this.generateNewNumber();
        this.resetRoundData();
        this.updateDisplay();
        this.startTimer();
    }
    
    generateNewNumber() {
        const config = this.levelConfigs[this.currentLevel];
        this.secretNumber = Math.floor(Math.random() * (config.max - config.min + 1)) + config.min;
        this.attempts = 0;
        this.guessHistory = [];
        
        // Update display
        document.getElementById('min-number').textContent = config.min;
        document.getElementById('max-number').textContent = config.max;
        document.getElementById('attempts-left').textContent = this.maxAttempts;
        document.getElementById('guess-input').value = '';
        document.getElementById('feedback-message').classList.add('hidden');
        
        this.updateGuessHistory();
    }
    
    resetRoundData() {
        this.attempts = 0;
        this.guessHistory = [];
        document.getElementById('guess-input').disabled = false;
        document.getElementById('submit-guess-btn').disabled = false;
    }
    
    submitGuess() {
        if (!this.isGameActive) return;
        
        const guessInput = document.getElementById('guess-input');
        const guess = parseInt(guessInput.value);
        
        if (isNaN(guess) || guess < this.levelConfigs[this.currentLevel].min || guess > this.levelConfigs[this.currentLevel].max) {
            this.showFeedback('أدخل رقماً صحيحاً ضمن النطاق المحدد!', 'warning');
            return;
        }
        
        this.attempts++;
        this.guessHistory.push({
            guess: guess,
            result: this.analyzeGuess(guess)
        });
        
        this.updateGuessHistory();
        this.updateDisplay();
        
        if (guess === this.secretNumber) {
            this.winRound();
        } else if (this.attempts >= this.maxAttempts) {
            this.loseRound();
        } else {
            this.showFeedback(this.getFeedbackMessage(guess), this.getFeedbackType(guess));
        }
        
        guessInput.value = '';
    }
    
    analyzeGuess(guess) {
        if (guess === this.secretNumber) {
            return 'correct';
        } else if (guess < this.secretNumber) {
            return 'low';
        } else {
            return 'high';
        }
    }
    
    getFeedbackMessage(guess) {
        const difference = Math.abs(guess - this.secretNumber);
        
        if (guess < this.secretNumber) {
            if (difference <= 5) {
                return 'قريب جداً! الرقم أكبر قليلاً';
            } else if (difference <= 15) {
                return 'قريب! الرقم أكبر';
            } else {
                return 'الرقم أكبر بكثير';
            }
        } else {
            if (difference <= 5) {
                return 'قريب جداً! الرقم أصغر قليلاً';
            } else if (difference <= 15) {
                return 'قريب! الرقم أصغر';
            } else {
                return 'الرقم أصغر بكثير';
            }
        }
    }
    
    getFeedbackType(guess) {
        const difference = Math.abs(guess - this.secretNumber);
        if (difference <= 5) return 'close';
        if (difference <= 15) return 'medium';
        return 'far';
    }
    
    showFeedback(message, type) {
        const feedbackMessage = document.getElementById('feedback-message');
        const feedbackIcon = document.getElementById('feedback-icon');
        const feedbackText = document.getElementById('feedback-text');
        
        feedbackMessage.className = `feedback-message ${type}`;
        feedbackText.textContent = message;
        
        switch(type) {
            case 'close':
                feedbackIcon.className = 'feedback-icon fas fa-bullseye';
                break;
            case 'medium':
                feedbackIcon.className = 'feedback-icon fas fa-compass';
                break;
            case 'far':
                feedbackIcon.className = 'feedback-icon fas fa-search';
                break;
            case 'warning':
                feedbackIcon.className = 'feedback-icon fas fa-exclamation-triangle';
                break;
            default:
                feedbackIcon.className = 'feedback-icon fas fa-info-circle';
        }
        
        feedbackMessage.classList.remove('hidden');
    }
    
    updateGuessHistory() {
        const historyContainer = document.getElementById('guess-history');
        
        if (this.guessHistory.length === 0) {
            historyContainer.innerHTML = '<p class="no-guesses">لم تبدأ التخمين بعد</p>';
            return;
        }
        
        historyContainer.innerHTML = this.guessHistory.map((entry, index) => {
            const resultIcon = entry.result === 'correct' ? '✅' : 
                              entry.result === 'low' ? '⬆️' : '⬇️';
            const resultText = entry.result === 'correct' ? 'صحيح!' : 
                              entry.result === 'low' ? 'أكبر' : 'أصغر';
            
            return `
                <div class="history-item ${entry.result}">
                    <span class="guess-number">${entry.guess}</span>
                    <span class="guess-result">${resultIcon} ${resultText}</span>
                </div>
            `;
        }).join('');
    }
    
    showHint() {
        if (this.attempts === 0) {
            this.showFeedback('ابدأ بالتخمين أولاً للحصول على تلميح!', 'warning');
            return;
        }
        
        const range = this.levelConfigs[this.currentLevel].max - this.levelConfigs[this.currentLevel].min + 1;
        const hintRange = Math.ceil(range / 4);
        const hintMin = Math.max(this.levelConfigs[this.currentLevel].min, this.secretNumber - hintRange);
        const hintMax = Math.min(this.levelConfigs[this.currentLevel].max, this.secretNumber + hintRange);
        
        this.showFeedback(`تلميح: الرقم بين ${hintMin} و ${hintMax}`, 'close');
        
        // Deduct points for hint
        this.score = Math.max(0, this.score - 10);
        this.updateStats();
    }
    
    winRound() {
        this.stopTimer();
        this.roundsWon++;
        
        // Calculate points based on attempts and time
        const basePoints = 50;
        const attemptBonus = Math.max(0, (this.maxAttempts - this.attempts) * 10);
        const timeBonus = Math.max(0, Math.floor((this.timeLimit - this.getElapsedTime()) / 10));
        const totalPoints = basePoints + attemptBonus + timeBonus;
        
        this.score += totalPoints;
        this.showRoundResult(true, totalPoints);
        this.updateStats();
        
        // Add score to global score
        addScore(totalPoints);
        playSuccessSound();
    }
    
    loseRound() {
        this.stopTimer();
        this.showRoundResult(false, 0);
        playErrorSound();
    }
    
    showRoundResult(won, points) {
        const resultPanel = document.getElementById('round-result-panel');
        const resultIcon = document.getElementById('round-result-icon');
        const resultTitle = document.getElementById('round-result-title');
        
        document.getElementById('game-area').classList.add('hidden');
        document.getElementById('secret-number-reveal').textContent = this.secretNumber;
        document.getElementById('attempts-used').textContent = this.attempts;
        document.getElementById('round-points').textContent = points;
        
        if (won) {
            resultIcon.innerHTML = '<i class="fas fa-trophy"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'ممتاز! وجدت الرقم!';
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'انتهت المحاولات!';
        }
        
        resultPanel.classList.remove('hidden');
    }
    
    nextRound() {
        this.round++;
        
        if (this.round > this.totalRounds) {
            this.completeGame();
        } else {
            this.updateStats();
            this.startNewRound();
        }
    }
    
    completeGame() {
        document.getElementById('round-result-panel').classList.add('hidden');
        
        // Calculate analysis skill
        const winRate = (this.roundsWon / this.totalRounds) * 100;
        let analysisSkill, performanceMessage;
        
        if (winRate >= 80) {
            analysisSkill = 'عبقري';
            performanceMessage = 'مهارات تحليلية استثنائية! أنت محلل ممتاز!';
        } else if (winRate >= 60) {
            analysisSkill = 'ممتاز';
            performanceMessage = 'أداء رائع في التحليل المنطقي والاستنتاج!';
        } else if (winRate >= 40) {
            analysisSkill = 'جيد';
            performanceMessage = 'مهارات جيدة، استمر في التدريب لتحسين قدرتك على التحليل!';
        } else {
            analysisSkill = 'يحتاج تطوير';
            performanceMessage = 'تحتاج لمزيد من التدريب على التفكير المنطقي والتحليل!';
        }
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('rounds-won').textContent = this.roundsWon;
        document.getElementById('analysis-skill').textContent = analysisSkill;
        document.getElementById('performance-message').textContent = performanceMessage;
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    startTimer() {
        const timeRemaining = document.getElementById('time-remaining');
        const timerFill = document.getElementById('timer-fill');
        
        let currentTime = this.timeLimit;
        timeRemaining.textContent = currentTime;
        timerFill.style.width = '100%';
        timerFill.style.transition = `width ${this.timeLimit}s linear`;
        
        setTimeout(() => {
            timerFill.style.width = '0%';
        }, 100);
        
        this.timer = setInterval(() => {
            currentTime--;
            timeRemaining.textContent = currentTime;
            
            if (currentTime <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    getElapsedTime() {
        const timeRemaining = parseInt(document.getElementById('time-remaining').textContent);
        return this.timeLimit - timeRemaining;
    }
    
    timeUp() {
        this.stopTimer();
        this.loseRound();
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.roundsWon = 0;
        this.hideAllPanels();
        this.showInstructions();
    }
    
    increaseDifficulty() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
            this.updateGameConfig();
        }
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('time-remaining').textContent = this.timeLimit;
    }
    
    updateDisplay() {
        document.getElementById('attempts-left').textContent = this.maxAttempts - this.attempts;
    }
    
    hideAllPanels() {
        const panels = ['round-result-panel', 'game-complete-panel'];
        panels.forEach(panelId => {
            document.getElementById(panelId).classList.add('hidden');
        });
    }
}

// Function to load the guess number game
function loadGuessNumberGame() {
    new GuessNumberGame(gameArea);
}
