/* Logic Puzzle Game Styles */
.logic-puzzle-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

.stat-box {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.2);
    min-width: 160px;
    justify-content: center;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.stat-box i {
    font-size: 1.3rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 35px;
    font-weight: 700;
}

.instructions-content h3 i {
    color: #667eea;
    margin-left: 15px;
}

.instruction-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 35px;
}

.step-card {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 25px;
    border-radius: 20px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.step-card i {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 15px;
}

.step-card h4 {
    color: #2d3748;
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.step-card p {
    color: #4a5568;
    line-height: 1.5;
    font-size: 1rem;
}

.level-selector {
    margin: 35px 0;
}

.level-selector h4 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.level-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.level-btn {
    padding: 15px 25px;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    min-width: 140px;
}

.level-btn:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);
}

.level-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

.level-btn i {
    font-size: 1.5rem;
}

.start-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 30px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(72, 187, 120, 0.4);
}

/* Puzzle Area */
.puzzle-area {
    background: white;
    border-radius: 25px;
    padding: 35px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.puzzle-header {
    text-align: center;
    margin-bottom: 35px;
}

.puzzle-header h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: #4a5568;
    font-weight: 600;
    font-size: 1.1rem;
}

.timer-bar {
    width: 250px;
    height: 12px;
    background: #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timer-fill {
    height: 100%;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 6px;
    transition: width 0.1s ease;
}

.pattern-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin: 40px 0;
    flex-wrap: wrap;
}

.pattern-sequence {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.pattern-item {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    animation: patternAppear 0.6s ease-out;
    transition: all 0.3s ease;
}

.pattern-item:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.question-mark {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 20px;
    animation: questionPulse 2s infinite;
}

@keyframes patternAppear {
    0% { transform: scale(0) rotate(180deg); opacity: 0; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

@keyframes questionPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.answer-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 20px;
    max-width: 500px;
    margin: 0 auto 30px;
}

.answer-option {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    background: white;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 auto;
}

.answer-option:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: #667eea;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.puzzle-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.control-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.hint-btn {
    color: #ed8936;
    border-color: #ed8936;
}

.hint-btn:hover {
    background: #ed8936;
    color: white;
    transform: translateY(-2px);
}

.skip-btn {
    color: #718096;
    border-color: #718096;
}

.skip-btn:hover {
    background: #718096;
    color: white;
    transform: translateY(-2px);
}

.hint-display {
    background: linear-gradient(135deg, #fef5e7, #fed7aa);
    border: 2px solid #ed8936;
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    animation: slideDown 0.5s ease;
}

.hint-content {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #9c4221;
}

.hint-content i {
    font-size: 1.5rem;
}

.hint-content p {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Result Panel */
.result-panel {
    background: white;
    border-radius: 25px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.result-content {
    max-width: 400px;
    margin: 0 auto;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.result-icon.success {
    color: #48bb78;
}

.result-icon.error {
    color: #f56565;
}

.result-icon.timeout {
    color: #ed8936;
}

.result-icon.skipped {
    color: #718096;
}

.result-content h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

.result-content p {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.1rem;
    line-height: 1.5;
}

.result-stats {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    color: #667eea;
    font-weight: 600;
    font-size: 1.1rem;
}

.next-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.next-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Game Complete Panel */
.game-complete-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.complete-trophy {
    font-size: 6rem;
    color: #ffd700;
    margin-bottom: 25px;
    animation: bounce 2s infinite;
}

.complete-content h2 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 35px;
    font-weight: 700;
}

.final-results {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.result-stat {
    background: #f7fafc;
    padding: 25px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 150px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.result-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.result-stat i {
    font-size: 2.5rem;
    color: #667eea;
}

.stat-details {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.achievement-message {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 35px;
}

.achievement-message p {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.complete-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-8px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .logic-puzzle-container {
        padding: 15px;
    }
    
    .game-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-box {
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-steps {
        grid-template-columns: 1fr;
    }
    
    .level-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .level-btn {
        width: 100%;
        max-width: 250px;
    }
    
    .pattern-display {
        flex-direction: column;
        gap: 30px;
    }
    
    .pattern-sequence {
        gap: 10px;
    }
    
    .pattern-item, .question-mark {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .answer-options {
        grid-template-columns: repeat(2, 1fr);
        max-width: 300px;
    }
    
    .answer-option {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    
    .timer-bar {
        width: 200px;
    }
    
    .final-results {
        flex-direction: column;
        align-items: center;
    }
    
    .complete-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .puzzle-header h3 {
        font-size: 1.5rem;
    }
    
    .pattern-item, .question-mark {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .answer-option {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }
    
    .timer-display {
        flex-direction: column;
        gap: 10px;
    }
    
    .timer-bar {
        width: 150px;
    }
    
    .puzzle-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .control-btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
}
