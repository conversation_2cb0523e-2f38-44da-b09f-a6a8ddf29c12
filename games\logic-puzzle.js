// Logic Puzzle Game
class LogicPuzzleGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 12;
        this.isGameActive = false;
        this.currentPuzzle = null;
        this.timeLimit = 45; // seconds to answer (will be updated based on level)
        this.timer = null;
        this.correctAnswers = 0;
        
        // Puzzle patterns for different difficulty levels
        this.puzzleTypes = {
            1: [ // Easy - Simple patterns
                {
                    type: 'shape_sequence',
                    pattern: ['🔴', '🔵', '🔴', '🔵'],
                    options: ['🔴', '🔵', '🟡', '🟢'],
                    correct: 0,
                    hint: 'الألوان تتكرر بنمط ثابت'
                },
                {
                    type: 'size_sequence',
                    pattern: ['⚫', '⚪', '⚫', '⚪'],
                    options: ['⚫', '⚪', '🔴', '🔵'],
                    correct: 0,
                    hint: 'الأشكال تتبادل بين الأسود والأبيض'
                },
                {
                    type: 'number_sequence',
                    pattern: ['1️⃣', '2️⃣', '3️⃣', '4️⃣'],
                    options: ['5️⃣', '1️⃣', '3️⃣', '2️⃣'],
                    correct: 0,
                    hint: 'الأرقام تزيد بواحد في كل مرة'
                }
            ],
            2: [ // Medium - More complex patterns
                {
                    type: 'shape_rotation',
                    pattern: ['🔺', '🔻', '🔺', '🔻'],
                    options: ['🔺', '🔻', '🔶', '🔷'],
                    correct: 0,
                    hint: 'المثلثات تتناوب في الاتجاه'
                },
                {
                    type: 'color_progression',
                    pattern: ['🟥', '🟧', '🟨', '🟩'],
                    options: ['🟦', '🟪', '🟫', '⬛'],
                    correct: 0,
                    hint: 'الألوان تتبع ترتيب قوس قزح'
                },
                {
                    type: 'mathematical',
                    pattern: ['2️⃣', '4️⃣', '6️⃣', '8️⃣'],
                    options: ['🔟', '1️⃣', '9️⃣', '7️⃣'],
                    correct: 0,
                    hint: 'الأرقام الزوجية تزيد بـ 2'
                }
            ],
            3: [ // Hard - Complex logical patterns
                {
                    type: 'complex_sequence',
                    pattern: ['🌟', '⭐', '✨', '🌟'],
                    options: ['⭐', '✨', '🌟', '💫'],
                    correct: 0,
                    hint: 'النجوم تتكرر بنمط من ثلاث عناصر'
                },
                {
                    type: 'logical_progression',
                    pattern: ['🐣', '🐤', '🐔', '🥚'],
                    options: ['🐣', '🐤', '🐔', '🦆'],
                    correct: 0,
                    hint: 'دورة حياة الدجاج'
                },
                {
                    type: 'spatial_reasoning',
                    pattern: ['⬆️', '➡️', '⬇️', '⬅️'],
                    options: ['⬆️', '↗️', '↘️', '↙️'],
                    correct: 0,
                    hint: 'الاتجاهات تدور في اتجاه عقارب الساعة'
                }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="logic-puzzle-container">
                <div class="game-header">
                    <div class="game-stats">
                        <div class="stat-box">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-box">
                            <i class="fas fa-puzzle-piece"></i>
                            <span>اللغز: <span id="current-round">1</span>/<span id="total-rounds">12</span></span>
                        </div>
                        <div class="stat-box">
                            <i class="fas fa-brain"></i>
                            <span>المستوى: <span id="current-level">1</span></span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-lightbulb"></i> ألغاز منطقية ممتعة</h3>
                        <div class="instruction-steps">
                            <div class="step-card">
                                <i class="fas fa-search"></i>
                                <h4>ادرس النمط</h4>
                                <p>انظر بعناية إلى تسلسل الأشكال أو الألوان</p>
                            </div>
                            <div class="step-card">
                                <i class="fas fa-brain"></i>
                                <h4>فكر منطقياً</h4>
                                <p>ما هو القانون الذي يحكم هذا التسلسل؟</p>
                            </div>
                            <div class="step-card">
                                <i class="fas fa-hand-pointer"></i>
                                <h4>اختر الإجابة</h4>
                                <p>اختر العنصر الذي يكمل النمط بشكل صحيح</p>
                            </div>
                        </div>
                        <div class="level-selector">
                            <h4>اختر مستوى الصعوبة:</h4>
                            <div class="level-buttons">
                                <button class="level-btn active" data-level="1">
                                    <i class="fas fa-baby"></i>
                                    مبتدئ (6-8 سنوات)
                                </button>
                                <button class="level-btn" data-level="2">
                                    <i class="fas fa-child"></i>
                                    متوسط (8-10 سنوات)
                                </button>
                                <button class="level-btn" data-level="3">
                                    <i class="fas fa-user-graduate"></i>
                                    متقدم (10-12 سنة)
                                </button>
                            </div>
                        </div>
                        <button id="start-puzzle-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ حل الألغاز
                        </button>
                    </div>
                </div>
                
                <div id="puzzle-area" class="puzzle-area hidden">
                    <div class="puzzle-header">
                        <h3>ما هو العنصر التالي في هذا النمط؟</h3>
                        <div class="timer-display">
                            <i class="fas fa-clock"></i>
                            <span id="time-remaining">30</span> ثانية
                            <div class="timer-bar">
                                <div id="timer-fill" class="timer-fill"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pattern-display">
                        <div id="pattern-sequence" class="pattern-sequence">
                            <!-- Pattern will be displayed here -->
                        </div>
                        <div class="question-mark">
                            <span>؟</span>
                        </div>
                    </div>
                    
                    <div id="answer-options" class="answer-options">
                        <!-- Answer options will be generated here -->
                    </div>
                    
                    <div class="puzzle-controls">
                        <button id="hint-btn" class="control-btn hint-btn">
                            <i class="fas fa-lightbulb"></i>
                            تلميح
                        </button>
                        <button id="skip-btn" class="control-btn skip-btn">
                            <i class="fas fa-forward"></i>
                            تخطي
                        </button>
                    </div>
                    
                    <div id="hint-display" class="hint-display hidden">
                        <div class="hint-content">
                            <i class="fas fa-info-circle"></i>
                            <p id="hint-text"></p>
                        </div>
                    </div>
                </div>
                
                <div id="result-panel" class="result-panel hidden">
                    <div class="result-content">
                        <div id="result-icon" class="result-icon"></div>
                        <h3 id="result-title"></h3>
                        <p id="result-message"></p>
                        <div class="result-stats">
                            <span>النقاط المكتسبة: <span id="earned-points">0</span></span>
                        </div>
                        <button id="next-puzzle-btn" class="next-btn">
                            <i class="fas fa-arrow-left"></i>
                            اللغز التالي
                        </button>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-trophy complete-trophy"></i>
                        <h2>مبروك! أكملت جميع الألغاز</h2>
                        <div class="final-results">
                            <div class="result-stat">
                                <i class="fas fa-star"></i>
                                <div class="stat-details">
                                    <span class="stat-number" id="final-score">0</span>
                                    <span class="stat-label">النقاط الإجمالية</span>
                                </div>
                            </div>
                            <div class="result-stat">
                                <i class="fas fa-check-circle"></i>
                                <div class="stat-details">
                                    <span class="stat-number" id="correct-count">0</span>
                                    <span class="stat-label">إجابات صحيحة</span>
                                </div>
                            </div>
                            <div class="result-stat">
                                <i class="fas fa-percentage"></i>
                                <div class="stat-details">
                                    <span class="stat-number" id="success-rate">0%</span>
                                    <span class="stat-label">معدل النجاح</span>
                                </div>
                            </div>
                        </div>
                        <div class="achievement-message">
                            <p id="achievement-text"></p>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="harder-level-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                مستوى أصعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Level selection
        document.querySelectorAll('.level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.level-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentLevel = parseInt(e.target.dataset.level);
                this.updateTimeLimit();
            });
        });
        
        document.getElementById('start-puzzle-btn').addEventListener('click', () => this.startGame());
        document.getElementById('hint-btn').addEventListener('click', () => this.showHint());
        document.getElementById('skip-btn').addEventListener('click', () => this.skipPuzzle());
        document.getElementById('next-puzzle-btn').addEventListener('click', () => this.nextPuzzle());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('harder-level-btn').addEventListener('click', () => this.nextLevel());
    }
    
    updateTimeLimit() {
        this.timeLimit = this.currentLevel === 1 ? 45 : (this.currentLevel === 2 ? 35 : 25);
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('puzzle-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.correctAnswers = 0;
        this.updateStats();
        this.loadNextPuzzle();
    }
    
    loadNextPuzzle() {
        this.hideAllPanels();
        this.generatePuzzle();
        this.startTimer();
    }
    
    generatePuzzle() {
        const puzzles = this.puzzleTypes[this.currentLevel];
        const randomPuzzle = puzzles[Math.floor(Math.random() * puzzles.length)];
        
        // Create a copy and modify it for variety
        this.currentPuzzle = JSON.parse(JSON.stringify(randomPuzzle));
        
        // Add some randomization to make puzzles unique
        this.randomizePuzzle();
        
        this.displayPuzzle();
    }
    
    randomizePuzzle() {
        // This method adds variety to the base patterns
        const puzzle = this.currentPuzzle;
        
        if (puzzle.type === 'shape_sequence') {
            const shapes = ['🔴', '🔵', '🟡', '🟢', '🟠', '🟣'];
            const selectedShapes = this.shuffleArray([...shapes]).slice(0, 2);
            puzzle.pattern = [selectedShapes[0], selectedShapes[1], selectedShapes[0], selectedShapes[1]];
            puzzle.options = this.shuffleArray([selectedShapes[0], selectedShapes[1], ...this.shuffleArray(shapes.filter(s => !selectedShapes.includes(s))).slice(0, 2)]);
            puzzle.correct = puzzle.options.indexOf(selectedShapes[0]);
        }
        
        // Add more randomization for other puzzle types...
    }
    
    displayPuzzle() {
        const patternSequence = document.getElementById('pattern-sequence');
        const answerOptions = document.getElementById('answer-options');
        
        // Clear previous content
        patternSequence.innerHTML = '';
        answerOptions.innerHTML = '';
        
        // Display pattern
        this.currentPuzzle.pattern.forEach((item, index) => {
            const patternItem = document.createElement('div');
            patternItem.className = 'pattern-item';
            patternItem.textContent = item;
            patternItem.style.animationDelay = `${index * 0.2}s`;
            patternSequence.appendChild(patternItem);
        });
        
        // Display answer options
        this.currentPuzzle.options.forEach((option, index) => {
            const optionBtn = document.createElement('button');
            optionBtn.className = 'answer-option';
            optionBtn.textContent = option;
            optionBtn.addEventListener('click', () => this.selectAnswer(index));
            answerOptions.appendChild(optionBtn);
        });
        
        // Hide hint initially
        document.getElementById('hint-display').classList.add('hidden');
    }
    
    selectAnswer(selectedIndex) {
        if (!this.isGameActive) return;
        
        this.stopTimer();
        const isCorrect = selectedIndex === this.currentPuzzle.correct;
        
        // Calculate points
        let points = 0;
        if (isCorrect) {
            points = 15 + (this.currentLevel * 5);
            // Time bonus
            const timeBonus = Math.max(0, this.timeLimit - 10) * 2;
            points += timeBonus;
            this.correctAnswers++;
        }
        
        this.score += points;
        this.showResult(isCorrect, points);
        this.updateStats();
        
        // Add score to global score
        addScore(points);
    }
    
    showHint() {
        const hintDisplay = document.getElementById('hint-display');
        const hintText = document.getElementById('hint-text');
        
        hintText.textContent = this.currentPuzzle.hint;
        hintDisplay.classList.remove('hidden');
        
        // Deduct points for using hint
        this.score = Math.max(0, this.score - 5);
        this.updateStats();
    }
    
    skipPuzzle() {
        this.stopTimer();
        this.showResult(false, 0, true);
    }
    
    startTimer() {
        const timeRemaining = document.getElementById('time-remaining');
        const timerFill = document.getElementById('timer-fill');
        
        let currentTime = this.timeLimit;
        timeRemaining.textContent = currentTime;
        timerFill.style.width = '100%';
        timerFill.style.transition = `width ${this.timeLimit}s linear`;
        
        setTimeout(() => {
            timerFill.style.width = '0%';
        }, 100);
        
        this.timer = setInterval(() => {
            currentTime--;
            timeRemaining.textContent = currentTime;
            
            if (currentTime <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        this.showResult(false, 0, false, true);
    }
    
    showResult(isCorrect, points, skipped = false, timeOut = false) {
        const resultPanel = document.getElementById('result-panel');
        const resultIcon = document.getElementById('result-icon');
        const resultTitle = document.getElementById('result-title');
        const resultMessage = document.getElementById('result-message');
        const earnedPoints = document.getElementById('earned-points');
        
        document.getElementById('puzzle-area').classList.add('hidden');
        
        if (timeOut) {
            resultIcon.innerHTML = '<i class="fas fa-clock"></i>';
            resultIcon.className = 'result-icon timeout';
            resultTitle.textContent = 'انتهى الوقت!';
            resultMessage.textContent = 'حاول أن تكون أسرع في المرة القادمة';
        } else if (skipped) {
            resultIcon.innerHTML = '<i class="fas fa-forward"></i>';
            resultIcon.className = 'result-icon skipped';
            resultTitle.textContent = 'تم التخطي';
            resultMessage.textContent = 'لا تستسلم! حاول حل اللغز التالي';
        } else if (isCorrect) {
            resultIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'ممتاز! إجابة صحيحة';
            resultMessage.textContent = 'لديك تفكير منطقي رائع!';
            playSuccessSound();
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'إجابة خاطئة';
            resultMessage.textContent = 'لا بأس، ستنجح في اللغز التالي!';
            playErrorSound();
        }
        
        earnedPoints.textContent = points;
        resultPanel.classList.remove('hidden');
    }
    
    nextPuzzle() {
        this.round++;
        
        if (this.round > this.totalRounds) {
            this.completeGame();
        } else {
            this.updateStats();
            this.loadNextPuzzle();
        }
    }
    
    completeGame() {
        document.getElementById('result-panel').classList.add('hidden');
        
        // Calculate final statistics
        const successRate = Math.round((this.correctAnswers / this.totalRounds) * 100);
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('correct-count').textContent = this.correctAnswers;
        document.getElementById('success-rate').textContent = successRate;
        
        // Achievement message
        const achievementText = document.getElementById('achievement-text');
        if (successRate >= 90) {
            achievementText.textContent = 'عبقري! لديك ذكاء منطقي استثنائي!';
        } else if (successRate >= 70) {
            achievementText.textContent = 'أداء رائع! تفكيرك المنطقي ممتاز!';
        } else if (successRate >= 50) {
            achievementText.textContent = 'أداء جيد! استمر في التدريب لتحسين مهاراتك!';
        } else {
            achievementText.textContent = 'لا تستسلم! الممارسة تجعلك أفضل!';
        }
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.correctAnswers = 0;
        this.hideAllPanels();
        this.showInstructions();
    }
    
    nextLevel() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.level-btn').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
            this.updateTimeLimit();
        }
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('current-level').textContent = this.currentLevel;
    }
    
    hideAllPanels() {
        const panels = ['result-panel', 'game-complete-panel'];
        panels.forEach(panelId => {
            document.getElementById(panelId).classList.add('hidden');
        });
    }
    
    shuffleArray(array) {
        const newArray = [...array];
        for (let i = newArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
        }
        return newArray;
    }
}

// Function to load the logic puzzle game
function loadLogicPuzzleGame() {
    new LogicPuzzleGame(gameArea);
}
