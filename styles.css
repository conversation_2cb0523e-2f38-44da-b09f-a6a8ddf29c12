/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.main-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4a5568;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.main-title i {
    color: #667eea;
    margin-left: 15px;
    animation: pulse 2s infinite;
}

.subtitle {
    font-size: 1.2rem;
    color: #718096;
    font-weight: 400;
}

/* Games Grid */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

/* Game Card Styles */
.game-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.game-card:hover::before {
    left: 100%;
}

.game-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: #667eea;
}

.game-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.game-card:hover .game-icon {
    transform: scale(1.2) rotate(5deg);
    color: #764ba2;
}

.game-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
}

.game-description {
    font-size: 1rem;
    color: #718096;
    margin-bottom: 15px;
    line-height: 1.5;
}

.game-stats {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Game Container */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 40px;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.back-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.game-info h2 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.score-board {
    display: flex;
    gap: 20px;
    font-weight: 600;
    color: #667eea;
}

.game-area {
    min-height: 400px;
    background: #f7fafc;
    border-radius: 15px;
    padding: 20px;
    border: 2px dashed #e2e8f0;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* Message Container */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 300px;
}

.message-content {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: slideIn 0.3s ease;
}

.message-content.success {
    border-left: 4px solid #48bb78;
}

.message-content.error {
    border-left: 4px solid #f56565;
}

.message-icon {
    font-size: 1.2rem;
}

.message-content.success .message-icon {
    color: #48bb78;
}

.message-content.error .message-icon {
    color: #f56565;
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    color: #718096;
    font-size: 0.9rem;
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .games-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .game-header {
        flex-direction: column;
        text-align: center;
    }
    
    .score-board {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 1.8rem;
    }
    
    .game-card {
        padding: 20px;
    }
    
    .game-icon {
        font-size: 2.5rem;
    }
}
