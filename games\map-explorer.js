// Map Explorer Game
class MapExplorerGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.totalStars = 0;
        this.currentLocation = null;
        this.completedLocations = new Set();
        this.isGameActive = false;
        this.playerPosition = { x: 50, y: 50 }; // Starting position in percentage
        
        // Map locations with different types of challenges
        this.mapLocations = {
            1: [ // Easy level
                {
                    id: 'castle',
                    name: 'القلعة السحرية',
                    icon: '🏰',
                    position: { x: 20, y: 30 },
                    challenge: {
                        type: 'math',
                        question: 'كم يساوي 5 + 3؟',
                        options: ['6', '7', '8', '9'],
                        correct: 2,
                        reward: 3
                    }
                },
                {
                    id: 'forest',
                    name: 'الغابة المسحورة',
                    icon: '🌲',
                    position: { x: 70, y: 20 },
                    challenge: {
                        type: 'sequence',
                        question: 'ما هو الرقم التالي في التسلسل: 2, 4, 6, ?',
                        options: ['7', '8', '9', '10'],
                        correct: 1,
                        reward: 3
                    }
                },
                {
                    id: 'mountain',
                    name: 'الجبل العالي',
                    icon: '⛰️',
                    position: { x: 80, y: 70 },
                    challenge: {
                        type: 'logic',
                        question: 'إذا كان لديك 10 تفاحات وأكلت 3، كم تبقى؟',
                        options: ['6', '7', '8', '13'],
                        correct: 1,
                        reward: 4
                    }
                },
                {
                    id: 'lake',
                    name: 'البحيرة الزرقاء',
                    icon: '🏞️',
                    position: { x: 30, y: 80 },
                    challenge: {
                        type: 'pattern',
                        question: 'أي شكل يكمل النمط: ⭐ 🌙 ⭐ ?',
                        options: ['⭐', '🌙', '☀️', '🌟'],
                        correct: 1,
                        reward: 3
                    }
                }
            ],
            2: [ // Medium level
                {
                    id: 'pyramid',
                    name: 'الهرم الذهبي',
                    icon: '🔺',
                    position: { x: 15, y: 25 },
                    challenge: {
                        type: 'math',
                        question: 'كم يساوي 12 × 3؟',
                        options: ['34', '35', '36', '37'],
                        correct: 2,
                        reward: 4
                    }
                },
                {
                    id: 'volcano',
                    name: 'البركان النشط',
                    icon: '🌋',
                    position: { x: 75, y: 15 },
                    challenge: {
                        type: 'logic',
                        question: 'إذا كان عمر أحمد ضعف عمر سارة، وعمر سارة 8 سنوات، كم عمر أحمد؟',
                        options: ['14', '15', '16', '17'],
                        correct: 2,
                        reward: 5
                    }
                },
                {
                    id: 'desert',
                    name: 'الصحراء الذهبية',
                    icon: '🏜️',
                    position: { x: 85, y: 75 },
                    challenge: {
                        type: 'sequence',
                        question: 'ما هو الرقم المفقود: 3, 6, 12, ?, 48',
                        options: ['20', '22', '24', '26'],
                        correct: 2,
                        reward: 5
                    }
                },
                {
                    id: 'island',
                    name: 'الجزيرة المفقودة',
                    icon: '🏝️',
                    position: { x: 25, y: 85 },
                    challenge: {
                        type: 'word',
                        question: 'أكمل الكلمة: مد _ _ ة',
                        options: ['رس', 'ين', 'ار', 'يق'],
                        correct: 0,
                        reward: 4
                    }
                }
            ],
            3: [ // Hard level
                {
                    id: 'spaceship',
                    name: 'المركبة الفضائية',
                    icon: '🚀',
                    position: { x: 10, y: 20 },
                    challenge: {
                        type: 'math',
                        question: 'كم يساوي (15 + 5) ÷ 4؟',
                        options: ['4', '5', '6', '7'],
                        correct: 1,
                        reward: 6
                    }
                },
                {
                    id: 'crystal',
                    name: 'كهف الكريستال',
                    icon: '💎',
                    position: { x: 80, y: 10 },
                    challenge: {
                        type: 'logic',
                        question: 'إذا كان A = 1, B = 2, C = 3، فما قيمة CAB؟',
                        options: ['312', '321', '123', '132'],
                        correct: 0,
                        reward: 7
                    }
                },
                {
                    id: 'portal',
                    name: 'البوابة السحرية',
                    icon: '🌀',
                    position: { x: 90, y: 80 },
                    challenge: {
                        type: 'pattern',
                        question: 'أي رقم يكمل النمط: 1, 4, 9, 16, ?',
                        options: ['20', '23', '25', '27'],
                        correct: 2,
                        reward: 6
                    }
                },
                {
                    id: 'treasure',
                    name: 'كنز القراصنة',
                    icon: '💰',
                    position: { x: 20, y: 90 },
                    challenge: {
                        type: 'riddle',
                        question: 'أنا أطير بلا جناح، وأبكي بلا عين، فمن أنا؟',
                        options: ['السحاب', 'الريح', 'المطر', 'الغيم'],
                        correct: 0,
                        reward: 8
                    }
                }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="map-explorer-container">
                <div class="game-header">
                    <div class="explorer-stats">
                        <div class="stat-item">
                            <i class="fas fa-star"></i>
                            <span>النجوم: <span id="total-stars">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-map-marked-alt"></i>
                            <span>المستوى: <span id="current-level">1</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span>المكتملة: <span id="completed-locations">0</span>/<span id="total-locations">4</span></span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-compass"></i> مغامرة استكشاف الخريطة</h3>
                        <div class="adventure-intro">
                            <p>انطلق في رحلة مثيرة لاستكشاف عوالم مختلفة وحل الألغاز المخفية!</p>
                        </div>
                        <div class="instruction-grid">
                            <div class="instruction-card">
                                <i class="fas fa-mouse-pointer"></i>
                                <h4>انقر على المواقع</h4>
                                <p>اختر موقعاً على الخريطة لبدء التحدي</p>
                            </div>
                            <div class="instruction-card">
                                <i class="fas fa-brain"></i>
                                <h4>حل الألغاز</h4>
                                <p>أجب على الأسئلة لكسب النجوم</p>
                            </div>
                            <div class="instruction-card">
                                <i class="fas fa-star"></i>
                                <h4>اجمع النجوم</h4>
                                <p>كلما حللت أكثر، كسبت نجوماً أكثر</p>
                            </div>
                            <div class="instruction-card">
                                <i class="fas fa-level-up-alt"></i>
                                <h4>تقدم للمستوى التالي</h4>
                                <p>أكمل جميع المواقع للانتقال للمستوى التالي</p>
                            </div>
                        </div>
                        <div class="level-selection">
                            <h4>اختر مستوى المغامرة:</h4>
                            <div class="level-options">
                                <button class="level-option active" data-level="1">
                                    <i class="fas fa-seedling"></i>
                                    <span>مبتدئ</span>
                                    <small>مغامرات بسيطة</small>
                                </button>
                                <button class="level-option" data-level="2">
                                    <i class="fas fa-mountain"></i>
                                    <span>متوسط</span>
                                    <small>تحديات أكبر</small>
                                </button>
                                <button class="level-option" data-level="3">
                                    <i class="fas fa-rocket"></i>
                                    <span>خبير</span>
                                    <small>مغامرات صعبة</small>
                                </button>
                            </div>
                        </div>
                        <button id="start-exploration-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ الاستكشاف
                        </button>
                    </div>
                </div>
                
                <div id="map-area" class="map-area hidden">
                    <div class="map-container">
                        <div id="game-map" class="game-map">
                            <div id="player-avatar" class="player-avatar">
                                <i class="fas fa-user-astronaut"></i>
                            </div>
                            <!-- Map locations will be generated here -->
                        </div>
                        <div class="map-legend">
                            <div class="legend-item">
                                <span class="legend-icon completed">✅</span>
                                <span>مكتمل</span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-icon available">🎯</span>
                                <span>متاح</span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-icon locked">🔒</span>
                                <span>مقفل</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="challenge-modal" class="challenge-modal hidden">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="location-name"></h3>
                            <button id="close-modal-btn" class="close-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="challenge-content">
                            <div class="challenge-question">
                                <h4 id="challenge-question-text"></h4>
                            </div>
                            <div id="challenge-options" class="challenge-options">
                                <!-- Options will be generated here -->
                            </div>
                            <div class="challenge-reward">
                                <i class="fas fa-star"></i>
                                <span>مكافأة: <span id="challenge-reward-amount">0</span> نجمة</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="result-modal" class="result-modal hidden">
                    <div class="result-content">
                        <div id="result-icon" class="result-icon"></div>
                        <h3 id="result-title"></h3>
                        <p id="result-message"></p>
                        <div class="result-reward">
                            <span>النجوم المكتسبة: <span id="earned-stars">0</span></span>
                        </div>
                        <button id="continue-btn" class="continue-btn">
                            <i class="fas fa-arrow-left"></i>
                            متابعة الاستكشاف
                        </button>
                    </div>
                </div>
                
                <div id="level-complete-modal" class="level-complete-modal hidden">
                    <div class="complete-content">
                        <i class="fas fa-trophy complete-trophy"></i>
                        <h2>مبروك! أكملت المستوى</h2>
                        <div class="level-stats">
                            <div class="level-stat">
                                <i class="fas fa-star"></i>
                                <span>إجمالي النجوم: <span id="level-total-stars">0</span></span>
                            </div>
                            <div class="level-stat">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>المواقع المكتملة: <span id="level-completed-count">0</span></span>
                            </div>
                        </div>
                        <div class="level-actions">
                            <button id="next-level-btn" class="action-btn primary">
                                <i class="fas fa-arrow-left"></i>
                                المستوى التالي
                            </button>
                            <button id="replay-level-btn" class="action-btn secondary">
                                <i class="fas fa-redo"></i>
                                إعادة اللعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Level selection
        document.querySelectorAll('.level-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.level-option').forEach(b => b.classList.remove('active'));
                e.target.closest('.level-option').classList.add('active');
                this.currentLevel = parseInt(e.target.closest('.level-option').dataset.level);
            });
        });
        
        document.getElementById('start-exploration-btn').addEventListener('click', () => this.startGame());
        document.getElementById('close-modal-btn').addEventListener('click', () => this.closeChallenge());
        document.getElementById('continue-btn').addEventListener('click', () => this.closeResult());
        document.getElementById('next-level-btn').addEventListener('click', () => this.nextLevel());
        document.getElementById('replay-level-btn').addEventListener('click', () => this.restartLevel());
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('map-area').classList.remove('hidden');
        this.isGameActive = true;
        this.totalStars = 0;
        this.completedLocations.clear();
        this.generateMap();
        this.updateStats();
    }
    
    generateMap() {
        const gameMap = document.getElementById('game-map');
        const locations = this.mapLocations[this.currentLevel];
        
        // Clear existing locations
        const existingLocations = gameMap.querySelectorAll('.map-location');
        existingLocations.forEach(loc => loc.remove());
        
        // Add locations to map
        locations.forEach(location => {
            const locationElement = document.createElement('div');
            locationElement.className = 'map-location available';
            locationElement.dataset.locationId = location.id;
            locationElement.style.left = location.position.x + '%';
            locationElement.style.top = location.position.y + '%';
            
            locationElement.innerHTML = `
                <div class="location-icon">${location.icon}</div>
                <div class="location-name">${location.name}</div>
                <div class="location-status">
                    <i class="fas fa-play"></i>
                </div>
            `;
            
            locationElement.addEventListener('click', () => this.openChallenge(location));
            gameMap.appendChild(locationElement);
        });
        
        // Position player avatar
        this.updatePlayerPosition();
    }
    
    updatePlayerPosition() {
        const playerAvatar = document.getElementById('player-avatar');
        playerAvatar.style.left = this.playerPosition.x + '%';
        playerAvatar.style.top = this.playerPosition.y + '%';
    }
    
    openChallenge(location) {
        if (this.completedLocations.has(location.id)) {
            return; // Already completed
        }
        
        this.currentLocation = location;
        
        // Update modal content
        document.getElementById('location-name').textContent = location.name;
        document.getElementById('challenge-question-text').textContent = location.challenge.question;
        document.getElementById('challenge-reward-amount').textContent = location.challenge.reward;
        
        // Generate options
        const optionsContainer = document.getElementById('challenge-options');
        optionsContainer.innerHTML = '';
        
        location.challenge.options.forEach((option, index) => {
            const optionBtn = document.createElement('button');
            optionBtn.className = 'challenge-option';
            optionBtn.textContent = option;
            optionBtn.addEventListener('click', () => this.selectAnswer(index));
            optionsContainer.appendChild(optionBtn);
        });
        
        document.getElementById('challenge-modal').classList.remove('hidden');
    }
    
    selectAnswer(selectedIndex) {
        const challenge = this.currentLocation.challenge;
        const isCorrect = selectedIndex === challenge.correct;
        
        let starsEarned = 0;
        if (isCorrect) {
            starsEarned = challenge.reward;
            this.totalStars += starsEarned;
            this.completedLocations.add(this.currentLocation.id);
            
            // Update location appearance
            const locationElement = document.querySelector(`[data-location-id="${this.currentLocation.id}"]`);
            locationElement.classList.remove('available');
            locationElement.classList.add('completed');
            locationElement.querySelector('.location-status').innerHTML = '<i class="fas fa-check"></i>';
            
            // Add score to global score
            addScore(starsEarned * 10);
        }
        
        this.closeChallenge();
        this.showResult(isCorrect, starsEarned);
        this.updateStats();
        
        // Check if level is complete
        if (this.completedLocations.size === this.mapLocations[this.currentLevel].length) {
            setTimeout(() => {
                this.completeLevel();
            }, 2000);
        }
    }
    
    closeChallenge() {
        document.getElementById('challenge-modal').classList.add('hidden');
    }
    
    showResult(isCorrect, starsEarned) {
        const resultIcon = document.getElementById('result-icon');
        const resultTitle = document.getElementById('result-title');
        const resultMessage = document.getElementById('result-message');
        const earnedStars = document.getElementById('earned-stars');
        
        if (isCorrect) {
            resultIcon.innerHTML = '<i class="fas fa-star"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'ممتاز!';
            resultMessage.textContent = 'لقد حللت اللغز بنجاح!';
            playSuccessSound();
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'حاول مرة أخرى';
            resultMessage.textContent = 'الإجابة غير صحيحة، لكن لا تستسلم!';
            playErrorSound();
        }
        
        earnedStars.textContent = starsEarned;
        document.getElementById('result-modal').classList.remove('hidden');
    }
    
    closeResult() {
        document.getElementById('result-modal').classList.add('hidden');
    }
    
    completeLevel() {
        document.getElementById('level-total-stars').textContent = this.totalStars;
        document.getElementById('level-completed-count').textContent = this.completedLocations.size;
        document.getElementById('level-complete-modal').classList.remove('hidden');
    }
    
    nextLevel() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.level-option').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
        }
        this.restartLevel();
    }
    
    restartLevel() {
        document.getElementById('level-complete-modal').classList.add('hidden');
        this.totalStars = 0;
        this.completedLocations.clear();
        this.generateMap();
        this.updateStats();
    }
    
    updateStats() {
        document.getElementById('total-stars').textContent = this.totalStars;
        document.getElementById('current-level').textContent = this.currentLevel;
        document.getElementById('completed-locations').textContent = this.completedLocations.size;
        document.getElementById('total-locations').textContent = this.mapLocations[this.currentLevel].length;
    }
}

// Function to load the map explorer game
function loadMapExplorerGame() {
    new MapExplorerGame(gameArea);
}
