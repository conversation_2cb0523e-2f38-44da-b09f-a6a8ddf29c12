/* Color Discovery Game Styles */
.color-discovery-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(102, 126, 234, 0.1);
    padding: 12px 20px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.2);
    min-width: 140px;
    justify-content: center;
}

.stat-item i {
    font-size: 1.2rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 2rem;
    margin-bottom: 30px;
}

.instructions-content h3 i {
    color: #667eea;
    margin-left: 10px;
}

.instruction-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 10px;
    text-align: right;
}

.instruction-item i {
    font-size: 1.5rem;
    color: #667eea;
    min-width: 30px;
}

.instruction-item p {
    color: #4a5568;
    margin: 0;
    line-height: 1.4;
}

.difficulty-selector {
    margin: 30px 0;
}

.difficulty-selector h4 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.difficulty-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.difficulty-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.difficulty-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.difficulty-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

.start-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Game Area */
.color-game-area {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.target-color-section {
    margin-bottom: 30px;
}

.target-color-section h3 {
    color: #2d3748;
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.target-color-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.target-color-box {
    width: 100px;
    height: 100px;
    border-radius: 15px;
    border: 4px solid #e2e8f0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.target-color-text {
    font-size: 2rem;
    font-weight: 700;
    padding: 15px 25px;
    border-radius: 15px;
    border: 3px solid #e2e8f0;
    background: white;
    min-width: 120px;
}

.timer-section {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: #4a5568;
    font-weight: 600;
}

.timer-bar {
    width: 300px;
    height: 10px;
    background: #e2e8f0;
    border-radius: 5px;
    overflow: hidden;
}

.timer-progress {
    height: 100%;
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    border-radius: 5px;
    transition: width 0.1s ease;
}

.color-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    max-width: 600px;
    margin: 0 auto 30px;
}

.color-option {
    padding: 15px 10px;
    border: 3px solid transparent;
    border-radius: 15px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-option:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: #ffffff;
}

/* Result Feedback */
.result-feedback {
    background: #f7fafc;
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
    animation: slideIn 0.5s ease;
}

.feedback-content {
    text-align: center;
}

.feedback-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.feedback-icon.success {
    color: #48bb78;
}

.feedback-icon.error {
    color: #f56565;
}

.feedback-icon.timeout {
    color: #ed8936;
}

.feedback-content h4 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #2d3748;
}

.feedback-content p {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.feedback-stats {
    color: #667eea;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Game Complete Panel */
.game-complete-panel {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.complete-icon {
    font-size: 5rem;
    color: #ffd700;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

.complete-content h2 {
    color: #2d3748;
    font-size: 2rem;
    margin-bottom: 30px;
}

.final-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.final-stat {
    background: #f7fafc;
    padding: 25px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 150px;
    border: 2px solid #e2e8f0;
}

.final-stat i {
    font-size: 2.5rem;
    color: #667eea;
}

.stat-info {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.performance-message {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.performance-message p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.complete-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .color-discovery-container {
        padding: 15px;
    }
    
    .game-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-list {
        grid-template-columns: 1fr;
    }
    
    .target-color-display {
        flex-direction: column;
    }
    
    .timer-bar {
        width: 250px;
    }
    
    .color-options {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .final-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .complete-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .target-color-box {
        width: 80px;
        height: 80px;
    }
    
    .target-color-text {
        font-size: 1.5rem;
        padding: 10px 15px;
    }
    
    .color-options {
        grid-template-columns: 1fr;
        max-width: 300px;
    }
    
    .color-option {
        font-size: 1rem;
        padding: 12px 8px;
    }
    
    .timer-bar {
        width: 200px;
    }
}
