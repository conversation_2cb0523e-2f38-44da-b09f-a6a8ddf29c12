/* Quick Count Game Styles */
.quick-count-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.stat-box {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px 20px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.2);
    min-width: 150px;
    justify-content: center;
}

.stat-box i {
    font-size: 1.2rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 30px;
}

.instructions-content h3 i {
    color: #667eea;
    margin-left: 10px;
}

.instruction-steps {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 200px;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.step p {
    color: #4a5568;
    text-align: center;
    line-height: 1.5;
}

.start-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Game Area */
.count-game-area {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Countdown Display */
.countdown-display {
    text-align: center;
}

.countdown-number {
    font-size: 6rem;
    font-weight: 700;
    color: #667eea;
    animation: countdownPulse 1s ease-in-out;
}

.countdown-display p {
    font-size: 1.5rem;
    color: #4a5568;
    margin-top: 20px;
}

@keyframes countdownPulse {
    0% { transform: scale(0.5); opacity: 0; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

/* Items Display */
.items-display {
    width: 100%;
    text-align: center;
}

.items-container {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: 15px;
    margin-bottom: 20px;
    overflow: hidden;
    border: 3px solid #e2e8f0;
}

.count-item {
    position: absolute;
    font-size: 2.5rem;
    animation: itemAppear 0.5s ease-out;
    transform: translate(-50%, -50%);
    cursor: default;
    user-select: none;
}

@keyframes itemAppear {
    0% { transform: translate(-50%, -50%) scale(0) rotate(180deg); opacity: 0; }
    100% { transform: translate(-50%, -50%) scale(1) rotate(0deg); opacity: 1; }
}

.display-timer {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.display-progress {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.1s ease;
}

/* Answer Panel */
.answer-panel {
    width: 100%;
    text-align: center;
}

.question-text h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 30px;
}

.question-text span {
    color: #667eea;
    font-weight: 700;
}

.answer-input-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.answer-input {
    padding: 15px 20px;
    font-size: 1.5rem;
    border: 3px solid #e2e8f0;
    border-radius: 15px;
    text-align: center;
    width: 150px;
    transition: all 0.3s ease;
}

.answer-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.answer-timer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: #4a5568;
    font-weight: 600;
}

.answer-progress {
    width: 200px;
    height: 8px;
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    border-radius: 4px;
    transition: width 0.1s ease;
}

/* Result Panel */
.result-panel {
    width: 100%;
    text-align: center;
}

.result-content {
    background: #f7fafc;
    padding: 30px;
    border-radius: 15px;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.result-icon.success {
    color: #48bb78;
}

.result-icon.error {
    color: #f56565;
}

.result-content h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #2d3748;
}

.result-details {
    margin-bottom: 30px;
}

.result-details p {
    margin: 10px 0;
    font-size: 1.1rem;
    color: #4a5568;
}

.result-details span {
    font-weight: 600;
    color: #667eea;
}

.next-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.next-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Game Complete Panel */
.game-complete-panel {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.complete-icon {
    font-size: 5rem;
    color: #ffd700;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

.complete-content h2 {
    color: #2d3748;
    font-size: 2rem;
    margin-bottom: 30px;
}

.final-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.final-stat {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 150px;
}

.final-stat i {
    font-size: 2rem;
    color: #667eea;
}

.final-stat span {
    font-weight: 600;
    color: #2d3748;
}

.complete-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .quick-count-container {
        padding: 15px;
    }
    
    .game-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-box {
        min-width: auto;
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-steps {
        flex-direction: column;
        align-items: center;
    }
    
    .items-container {
        height: 300px;
    }
    
    .count-item {
        font-size: 2rem;
    }
    
    .answer-input-container {
        flex-direction: column;
        align-items: center;
    }
    
    .answer-input {
        width: 200px;
    }
    
    .final-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .complete-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .countdown-number {
        font-size: 4rem;
    }
    
    .items-container {
        height: 250px;
    }
    
    .count-item {
        font-size: 1.8rem;
    }
    
    .question-text h3 {
        font-size: 1.5rem;
    }
    
    .answer-input {
        width: 150px;
        font-size: 1.3rem;
    }
}
