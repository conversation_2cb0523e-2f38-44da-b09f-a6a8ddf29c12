// Shape Builder Game
class ShapeBuilderGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.currentLevel = 1;
        this.score = 0;
        this.round = 1;
        this.totalRounds = 8;
        this.isGameActive = false;
        this.currentPattern = null;
        this.playerGrid = [];
        this.timeLimit = 90; // seconds per round (will be updated based on level)
        this.timer = null;
        this.gridSize = 4; // 4x4 grid
        
        // Shape patterns for different levels
        this.shapePatterns = {
            1: [ // Easy - Simple shapes
                {
                    name: 'مربع',
                    pattern: [
                        [1, 1, 0, 0],
                        [1, 1, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع مربعاً صغيراً في الزاوية'
                },
                {
                    name: 'خط أفقي',
                    pattern: [
                        [1, 1, 1, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع خطاً أفقياً من 3 مربعات'
                },
                {
                    name: 'خط عمودي',
                    pattern: [
                        [1, 0, 0, 0],
                        [1, 0, 0, 0],
                        [1, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع خطاً عمودياً من 3 مربعات'
                }
            ],
            2: [ // Medium - More complex shapes
                {
                    name: 'حرف L',
                    pattern: [
                        [1, 0, 0, 0],
                        [1, 0, 0, 0],
                        [1, 1, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع شكل حرف L'
                },
                {
                    name: 'حرف T',
                    pattern: [
                        [1, 1, 1, 0],
                        [0, 1, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع شكل حرف T'
                },
                {
                    name: 'مستطيل',
                    pattern: [
                        [1, 1, 1, 0],
                        [1, 1, 1, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع مستطيلاً 3×2'
                }
            ],
            3: [ // Hard - Complex shapes
                {
                    name: 'صليب',
                    pattern: [
                        [0, 1, 0, 0],
                        [1, 1, 1, 0],
                        [0, 1, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع شكل صليب'
                },
                {
                    name: 'درج',
                    pattern: [
                        [1, 0, 0, 0],
                        [1, 1, 0, 0],
                        [1, 1, 1, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع شكل درج'
                },
                {
                    name: 'ماسة',
                    pattern: [
                        [0, 1, 0, 0],
                        [1, 1, 1, 0],
                        [0, 1, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    hint: 'اصنع شكل ماسة'
                }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.showInstructions();
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="shape-builder-container">
                <div class="game-header">
                    <div class="builder-stats">
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span>النقاط: <span id="current-score">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-shapes"></i>
                            <span>الشكل: <span id="current-round">1</span>/<span id="total-rounds">8</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <span>الوقت: <span id="time-remaining">60</span>ث</span>
                        </div>
                    </div>
                </div>
                
                <div id="instructions-panel" class="instructions-panel">
                    <div class="instructions-content">
                        <h3><i class="fas fa-cubes"></i> بناء الأشكال الهندسية</h3>
                        <div class="game-intro">
                            <p>استخدم ذكاءك المكاني لبناء الأشكال المطلوبة!</p>
                        </div>
                        <div class="instruction-steps">
                            <div class="step-item">
                                <i class="fas fa-eye"></i>
                                <h4>انظر للشكل المطلوب</h4>
                                <p>ادرس الشكل الهدف بعناية</p>
                            </div>
                            <div class="step-item">
                                <i class="fas fa-mouse-pointer"></i>
                                <h4>انقر على المربعات</h4>
                                <p>اختر المربعات لبناء نفس الشكل</p>
                            </div>
                            <div class="step-item">
                                <i class="fas fa-check"></i>
                                <h4>تحقق من النتيجة</h4>
                                <p>اضغط تحقق عند الانتهاء</p>
                            </div>
                        </div>
                        <div class="difficulty-selection">
                            <h4>اختر مستوى الصعوبة:</h4>
                            <div class="difficulty-options">
                                <button class="difficulty-option active" data-level="1">
                                    <i class="fas fa-baby"></i>
                                    <span>مبتدئ</span>
                                    <small>أشكال بسيطة</small>
                                </button>
                                <button class="difficulty-option" data-level="2">
                                    <i class="fas fa-child"></i>
                                    <span>متوسط</span>
                                    <small>أشكال معقدة</small>
                                </button>
                                <button class="difficulty-option" data-level="3">
                                    <i class="fas fa-user-graduate"></i>
                                    <span>خبير</span>
                                    <small>تحدي صعب</small>
                                </button>
                            </div>
                        </div>
                        <button id="start-building-btn" class="start-btn">
                            <i class="fas fa-play"></i>
                            ابدأ البناء
                        </button>
                    </div>
                </div>
                
                <div id="game-area" class="builder-game-area hidden">
                    <div class="game-layout">
                        <div class="target-section">
                            <h3>الشكل المطلوب:</h3>
                            <div class="shape-info">
                                <h4 id="shape-name">الشكل</h4>
                                <div id="target-grid" class="target-grid">
                                    <!-- Target pattern will be shown here -->
                                </div>
                                <div class="shape-hint">
                                    <i class="fas fa-lightbulb"></i>
                                    <span id="shape-hint-text">تلميح الشكل</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="builder-section">
                            <h3>منطقة البناء:</h3>
                            <div id="player-grid" class="player-grid">
                                <!-- Player building grid -->
                            </div>
                            <div class="grid-controls">
                                <button id="clear-grid-btn" class="control-btn clear">
                                    <i class="fas fa-eraser"></i>
                                    مسح الكل
                                </button>
                                <button id="check-shape-btn" class="control-btn check">
                                    <i class="fas fa-check"></i>
                                    تحقق من الشكل
                                </button>
                                <button id="hint-btn" class="control-btn hint">
                                    <i class="fas fa-question"></i>
                                    تلميح
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="timer-section">
                        <div class="timer-bar">
                            <div id="timer-fill" class="timer-fill"></div>
                        </div>
                    </div>
                </div>
                
                <div id="result-panel" class="result-panel hidden">
                    <div class="result-content">
                        <div id="result-icon" class="result-icon"></div>
                        <h3 id="result-title"></h3>
                        <p id="result-message"></p>
                        <div class="result-stats">
                            <span>النقاط المكتسبة: <span id="earned-points">0</span></span>
                        </div>
                        <button id="next-shape-btn" class="next-btn">
                            <i class="fas fa-arrow-left"></i>
                            الشكل التالي
                        </button>
                    </div>
                </div>
                
                <div id="game-complete-panel" class="game-complete-panel hidden">
                    <div class="complete-content">
                        <i class="fas fa-medal complete-medal"></i>
                        <h2>مبروك! أكملت جميع الأشكال</h2>
                        <div class="final-results">
                            <div class="final-stat">
                                <i class="fas fa-star"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="final-score">0</span>
                                    <span class="stat-label">النقاط الإجمالية</span>
                                </div>
                            </div>
                            <div class="final-stat">
                                <i class="fas fa-shapes"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="shapes-completed">0</span>
                                    <span class="stat-label">أشكال مكتملة</span>
                                </div>
                            </div>
                            <div class="final-stat">
                                <i class="fas fa-brain"></i>
                                <div class="stat-info">
                                    <span class="stat-value" id="spatial-rating">ممتاز</span>
                                    <span class="stat-label">الذكاء المكاني</span>
                                </div>
                            </div>
                        </div>
                        <div class="achievement-badge">
                            <p id="achievement-message"></p>
                        </div>
                        <div class="complete-actions">
                            <button id="play-again-btn" class="action-btn primary">
                                <i class="fas fa-redo"></i>
                                العب مرة أخرى
                            </button>
                            <button id="harder-level-btn" class="action-btn secondary">
                                <i class="fas fa-level-up-alt"></i>
                                مستوى أصعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        // Difficulty selection
        document.querySelectorAll('.difficulty-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.difficulty-option').forEach(b => b.classList.remove('active'));
                e.target.closest('.difficulty-option').classList.add('active');
                this.currentLevel = parseInt(e.target.closest('.difficulty-option').dataset.level);
                this.updateTimeLimit();
            });
        });
        
        document.getElementById('start-building-btn').addEventListener('click', () => this.startGame());
        document.getElementById('clear-grid-btn').addEventListener('click', () => this.clearGrid());
        document.getElementById('check-shape-btn').addEventListener('click', () => this.checkShape());
        document.getElementById('hint-btn').addEventListener('click', () => this.showHint());
        document.getElementById('next-shape-btn').addEventListener('click', () => this.nextShape());
        document.getElementById('play-again-btn').addEventListener('click', () => this.restartGame());
        document.getElementById('harder-level-btn').addEventListener('click', () => this.nextLevel());
    }
    
    updateTimeLimit() {
        this.timeLimit = this.currentLevel === 1 ? 90 : (this.currentLevel === 2 ? 75 : 60);
    }
    
    showInstructions() {
        document.getElementById('instructions-panel').classList.remove('hidden');
    }
    
    startGame() {
        document.getElementById('instructions-panel').classList.add('hidden');
        document.getElementById('game-area').classList.remove('hidden');
        this.isGameActive = true;
        this.round = 1;
        this.score = 0;
        this.updateStats();
        this.loadNextShape();
    }
    
    loadNextShape() {
        this.hideAllPanels();
        this.generateShape();
        this.createGrids();
        this.startTimer();
    }
    
    generateShape() {
        const patterns = this.shapePatterns[this.currentLevel];
        this.currentPattern = patterns[Math.floor(Math.random() * patterns.length)];
        
        // Display target shape info
        document.getElementById('shape-name').textContent = this.currentPattern.name;
        document.getElementById('shape-hint-text').textContent = this.currentPattern.hint;
        
        this.displayTargetGrid();
    }
    
    displayTargetGrid() {
        const targetGrid = document.getElementById('target-grid');
        targetGrid.innerHTML = '';
        
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell target-cell';
                if (this.currentPattern.pattern[row][col] === 1) {
                    cell.classList.add('filled');
                }
                targetGrid.appendChild(cell);
            }
        }
    }
    
    createGrids() {
        // Initialize player grid
        this.playerGrid = Array(this.gridSize).fill().map(() => Array(this.gridSize).fill(0));
        
        const playerGridElement = document.getElementById('player-grid');
        playerGridElement.innerHTML = '';
        
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                const cell = document.createElement('div');
                cell.className = 'grid-cell player-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                cell.addEventListener('click', () => this.toggleCell(row, col));
                playerGridElement.appendChild(cell);
            }
        }
    }
    
    toggleCell(row, col) {
        if (!this.isGameActive) return;
        
        this.playerGrid[row][col] = this.playerGrid[row][col] === 1 ? 0 : 1;
        this.updatePlayerGrid();
    }
    
    updatePlayerGrid() {
        const cells = document.querySelectorAll('.player-cell');
        cells.forEach((cell, index) => {
            const row = Math.floor(index / this.gridSize);
            const col = index % this.gridSize;
            
            if (this.playerGrid[row][col] === 1) {
                cell.classList.add('filled');
            } else {
                cell.classList.remove('filled');
            }
        });
    }
    
    clearGrid() {
        this.playerGrid = Array(this.gridSize).fill().map(() => Array(this.gridSize).fill(0));
        this.updatePlayerGrid();
    }
    
    checkShape() {
        if (!this.isGameActive) return;
        
        this.stopTimer();
        const isCorrect = this.compareGrids();
        
        let points = 0;
        if (isCorrect) {
            points = 20 + (this.currentLevel * 10);
            // Time bonus
            const timeBonus = Math.max(0, this.timeLimit - 30) * 2;
            points += timeBonus;
        }
        
        this.score += points;
        this.showResult(isCorrect, points);
        this.updateStats();
        
        // Add score to global score
        addScore(points);
    }
    
    compareGrids() {
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                if (this.playerGrid[row][col] !== this.currentPattern.pattern[row][col]) {
                    return false;
                }
            }
        }
        return true;
    }
    
    showHint() {
        // Highlight one correct cell that's not filled yet
        for (let row = 0; row < this.gridSize; row++) {
            for (let col = 0; col < this.gridSize; col++) {
                if (this.currentPattern.pattern[row][col] === 1 && this.playerGrid[row][col] === 0) {
                    const cellIndex = row * this.gridSize + col;
                    const cell = document.querySelectorAll('.player-cell')[cellIndex];
                    cell.classList.add('hint-cell');
                    
                    setTimeout(() => {
                        cell.classList.remove('hint-cell');
                    }, 2000);
                    
                    // Deduct points for hint
                    this.score = Math.max(0, this.score - 5);
                    this.updateStats();
                    return;
                }
            }
        }
    }
    
    startTimer() {
        const timeRemaining = document.getElementById('time-remaining');
        const timerFill = document.getElementById('timer-fill');
        
        let currentTime = this.timeLimit;
        timeRemaining.textContent = currentTime;
        timerFill.style.width = '100%';
        timerFill.style.transition = `width ${this.timeLimit}s linear`;
        
        setTimeout(() => {
            timerFill.style.width = '0%';
        }, 100);
        
        this.timer = setInterval(() => {
            currentTime--;
            timeRemaining.textContent = currentTime;
            
            if (currentTime <= 0) {
                this.timeUp();
            }
        }, 1000);
    }
    
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    timeUp() {
        this.stopTimer();
        this.showResult(false, 0, true);
    }
    
    showResult(isCorrect, points, timeOut = false) {
        const resultPanel = document.getElementById('result-panel');
        const resultIcon = document.getElementById('result-icon');
        const resultTitle = document.getElementById('result-title');
        const resultMessage = document.getElementById('result-message');
        const earnedPoints = document.getElementById('earned-points');
        
        document.getElementById('game-area').classList.add('hidden');
        
        if (timeOut) {
            resultIcon.innerHTML = '<i class="fas fa-clock"></i>';
            resultIcon.className = 'result-icon timeout';
            resultTitle.textContent = 'انتهى الوقت!';
            resultMessage.textContent = 'حاول أن تكون أسرع في المرة القادمة';
        } else if (isCorrect) {
            resultIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            resultIcon.className = 'result-icon success';
            resultTitle.textContent = 'ممتاز! شكل صحيح';
            resultMessage.textContent = 'لديك ذكاء مكاني رائع!';
            playSuccessSound();
        } else {
            resultIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
            resultIcon.className = 'result-icon error';
            resultTitle.textContent = 'شكل غير صحيح';
            resultMessage.textContent = 'قارن شكلك بالشكل المطلوب وحاول مرة أخرى';
            playErrorSound();
        }
        
        earnedPoints.textContent = points;
        resultPanel.classList.remove('hidden');
    }
    
    nextShape() {
        this.round++;
        
        if (this.round > this.totalRounds) {
            this.completeGame();
        } else {
            this.updateStats();
            this.loadNextShape();
        }
    }
    
    completeGame() {
        document.getElementById('result-panel').classList.add('hidden');
        
        // Calculate final statistics
        const shapesCompleted = this.round - 1;
        const spatialRating = this.score > 150 ? 'عبقري' : (this.score > 100 ? 'ممتاز' : (this.score > 50 ? 'جيد' : 'مقبول'));
        
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('shapes-completed').textContent = shapesCompleted;
        document.getElementById('spatial-rating').textContent = spatialRating;
        
        // Achievement message
        const achievementMessage = document.getElementById('achievement-message');
        if (this.score > 150) {
            achievementMessage.textContent = 'مهندس معماري صغير! ذكاؤك المكاني استثنائي!';
        } else if (this.score > 100) {
            achievementMessage.textContent = 'بناء ماهر! لديك قدرة ممتازة على التصور المكاني!';
        } else {
            achievementMessage.textContent = 'استمر في التدريب لتطوير مهاراتك المكانية!';
        }
        
        document.getElementById('game-complete-panel').classList.remove('hidden');
    }
    
    restartGame() {
        this.round = 1;
        this.score = 0;
        this.hideAllPanels();
        this.showInstructions();
    }
    
    nextLevel() {
        if (this.currentLevel < 3) {
            this.currentLevel++;
            document.querySelectorAll('.difficulty-option').forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.level) === this.currentLevel) {
                    btn.classList.add('active');
                }
            });
            this.updateTimeLimit();
        }
        this.restartGame();
    }
    
    updateStats() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('current-round').textContent = this.round;
        document.getElementById('total-rounds').textContent = this.totalRounds;
        document.getElementById('time-remaining').textContent = this.timeLimit;
    }
    
    hideAllPanels() {
        const panels = ['result-panel', 'game-complete-panel'];
        panels.forEach(panelId => {
            document.getElementById(panelId).classList.add('hidden');
        });
    }
}

// Function to load the shape builder game
function loadShapeBuilderGame() {
    new ShapeBuilderGame(gameArea);
}
