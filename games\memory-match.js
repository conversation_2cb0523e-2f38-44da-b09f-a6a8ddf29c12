// Memory Match Game
class MemoryMatchGame {
    constructor(gameArea) {
        this.gameArea = gameArea;
        this.cards = [];
        this.flippedCards = [];
        this.matchedPairs = 0;
        this.moves = 0;
        this.timer = 0;
        this.timerInterval = null;
        this.level = 1;
        this.gameStarted = false;
        
        // Game configurations for different levels
        this.levelConfigs = {
            1: { pairs: 6, timeLimit: 60, emoji: ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊'] },
            2: { pairs: 8, timeLimit: 90, emoji: ['🍎', '🍌', '🍇', '🍓', '🍒', '🍑', '🥝', '🍍'] },
            3: { pairs: 10, timeLimit: 120, emoji: ['⚽', '🏀', '🏈', '⚾', '🎾', '🏐', '🏓', '🏸', '🥅', '⛳'] },
            4: { pairs: 12, timeLimit: 150, emoji: ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚'] }
        };
        
        this.init();
    }
    
    init() {
        this.createGameInterface();
        this.startLevel(1);
    }
    
    createGameInterface() {
        this.gameArea.innerHTML = `
            <div class="memory-game-container">
                <div class="game-controls">
                    <div class="game-stats">
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <span id="timer-display">00:00</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-hand-pointer"></i>
                            <span id="moves-display">0 حركات</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <span id="pairs-display">0/0 أزواج</span>
                        </div>
                    </div>
                    <div class="level-controls">
                        <button id="restart-btn" class="control-btn">
                            <i class="fas fa-redo"></i>
                            إعادة البدء
                        </button>
                        <button id="hint-btn" class="control-btn">
                            <i class="fas fa-lightbulb"></i>
                            تلميح
                        </button>
                    </div>
                </div>
                
                <div id="cards-container" class="cards-container">
                    <!-- Cards will be generated here -->
                </div>
                
                <div id="level-complete" class="level-complete hidden">
                    <div class="completion-content">
                        <i class="fas fa-star completion-icon"></i>
                        <h3>أحسنت! أكملت المستوى</h3>
                        <div class="completion-stats">
                            <p>الوقت: <span id="final-time"></span></p>
                            <p>الحركات: <span id="final-moves"></span></p>
                            <p>النقاط المكتسبة: <span id="earned-points"></span></p>
                        </div>
                        <div class="completion-actions">
                            <button id="next-level-btn" class="action-btn primary">
                                <i class="fas fa-arrow-left"></i>
                                المستوى التالي
                            </button>
                            <button id="replay-level-btn" class="action-btn secondary">
                                <i class="fas fa-redo"></i>
                                إعادة اللعب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.attachEventListeners();
    }
    
    attachEventListeners() {
        document.getElementById('restart-btn').addEventListener('click', () => this.restartLevel());
        document.getElementById('hint-btn').addEventListener('click', () => this.showHint());
        
        // Level completion events
        document.getElementById('next-level-btn').addEventListener('click', () => this.nextLevel());
        document.getElementById('replay-level-btn').addEventListener('click', () => this.restartLevel());
    }
    
    startLevel(level) {
        this.level = level;
        this.matchedPairs = 0;
        this.moves = 0;
        this.timer = 0;
        this.gameStarted = false;
        this.flippedCards = [];
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        
        const config = this.levelConfigs[level] || this.levelConfigs[4];
        this.createCards(config);
        this.updateDisplay();
        
        document.getElementById('level-complete').classList.add('hidden');
    }
    
    createCards(config) {
        const cardsContainer = document.getElementById('cards-container');
        cardsContainer.innerHTML = '';
        
        // Create pairs of cards
        const cardData = [];
        config.emoji.forEach((emoji, index) => {
            cardData.push({ id: index, emoji: emoji });
            cardData.push({ id: index, emoji: emoji });
        });
        
        // Shuffle cards
        this.shuffleArray(cardData);
        
        // Set grid layout based on number of cards
        const totalCards = cardData.length;
        const columns = Math.ceil(Math.sqrt(totalCards));
        cardsContainer.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
        
        // Create card elements
        this.cards = cardData.map((data, index) => {
            const card = document.createElement('div');
            card.className = 'memory-card';
            card.dataset.id = data.id;
            card.dataset.index = index;
            
            card.innerHTML = `
                <div class="card-inner">
                    <div class="card-front">
                        <i class="fas fa-question"></i>
                    </div>
                    <div class="card-back">
                        <span class="card-emoji">${data.emoji}</span>
                    </div>
                </div>
            `;
            
            card.addEventListener('click', () => this.flipCard(card, index));
            cardsContainer.appendChild(card);
            
            return {
                element: card,
                id: data.id,
                emoji: data.emoji,
                isFlipped: false,
                isMatched: false
            };
        });
    }
    
    flipCard(cardElement, cardIndex) {
        if (!this.gameStarted) {
            this.startTimer();
            this.gameStarted = true;
        }
        
        const card = this.cards[cardIndex];
        
        // Prevent flipping if card is already flipped or matched
        if (card.isFlipped || card.isMatched || this.flippedCards.length >= 2) {
            return;
        }
        
        // Flip the card
        card.isFlipped = true;
        cardElement.classList.add('flipped');
        this.flippedCards.push(cardIndex);
        
        // Check for match when two cards are flipped
        if (this.flippedCards.length === 2) {
            this.moves++;
            this.updateDisplay();
            
            setTimeout(() => {
                this.checkMatch();
            }, 1000);
        }
    }
    
    checkMatch() {
        const [firstIndex, secondIndex] = this.flippedCards;
        const firstCard = this.cards[firstIndex];
        const secondCard = this.cards[secondIndex];
        
        if (firstCard.id === secondCard.id) {
            // Match found
            firstCard.isMatched = true;
            secondCard.isMatched = true;
            firstCard.element.classList.add('matched');
            secondCard.element.classList.add('matched');
            
            this.matchedPairs++;
            this.updateDisplay();
            
            // Add score
            const timeBonus = Math.max(0, 60 - this.timer);
            const moveBonus = Math.max(0, 20 - this.moves);
            const points = 10 + timeBonus + moveBonus;
            addScore(points);
            
            playSuccessSound();
            
            // Check if level is complete
            if (this.matchedPairs === this.levelConfigs[this.level].pairs) {
                this.completeLevel();
            }
        } else {
            // No match
            firstCard.isFlipped = false;
            secondCard.isFlipped = false;
            firstCard.element.classList.remove('flipped');
            secondCard.element.classList.remove('flipped');
            
            playErrorSound();
        }
        
        this.flippedCards = [];
    }
    
    completeLevel() {
        clearInterval(this.timerInterval);
        
        const finalTime = this.formatTime(this.timer);
        const earnedPoints = this.calculateLevelPoints();
        
        document.getElementById('final-time').textContent = finalTime;
        document.getElementById('final-moves').textContent = this.moves;
        document.getElementById('earned-points').textContent = earnedPoints;
        
        document.getElementById('level-complete').classList.remove('hidden');
        
        // Hide next level button if this is the last level
        if (this.level >= 4) {
            document.getElementById('next-level-btn').style.display = 'none';
        }
    }
    
    calculateLevelPoints() {
        const basePoints = 50;
        const timeBonus = Math.max(0, this.levelConfigs[this.level].timeLimit - this.timer);
        const moveBonus = Math.max(0, (this.levelConfigs[this.level].pairs * 2) - this.moves) * 5;
        return basePoints + timeBonus + moveBonus;
    }
    
    nextLevel() {
        if (this.level < 4) {
            this.startLevel(this.level + 1);
        }
    }
    
    restartLevel() {
        this.startLevel(this.level);
    }
    
    showHint() {
        // Show two unmatched cards briefly
        const unmatchedCards = this.cards.filter(card => !card.isMatched && !card.isFlipped);
        if (unmatchedCards.length >= 2) {
            const hintCards = unmatchedCards.slice(0, 2);
            hintCards.forEach(card => {
                card.element.classList.add('hint');
                setTimeout(() => {
                    card.element.classList.remove('hint');
                }, 1500);
            });
        }
    }
    
    startTimer() {
        this.timerInterval = setInterval(() => {
            this.timer++;
            this.updateDisplay();
            
            // Check time limit
            if (this.timer >= this.levelConfigs[this.level].timeLimit) {
                this.gameOver();
            }
        }, 1000);
    }
    
    gameOver() {
        clearInterval(this.timerInterval);
        showMessage('انتهى الوقت! حاول مرة أخرى', 'error');
        setTimeout(() => {
            this.restartLevel();
        }, 2000);
    }
    
    updateDisplay() {
        document.getElementById('timer-display').textContent = this.formatTime(this.timer);
        document.getElementById('moves-display').textContent = `${this.moves} حركات`;
        document.getElementById('pairs-display').textContent = `${this.matchedPairs}/${this.levelConfigs[this.level].pairs} أزواج`;
    }
    
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}

// Function to load the memory match game
function loadMemoryMatchGame() {
    new MemoryMatchGame(gameArea);
}
