/* Missing Word Game Styles */
.missing-word-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    margin-bottom: 30px;
}

.word-stats {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(118, 75, 162, 0.1);
    padding: 15px 25px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #764ba2;
    border: 2px solid rgba(118, 75, 162, 0.2);
    min-width: 160px;
    justify-content: center;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(118, 75, 162, 0.2);
}

.stat-item i {
    font-size: 1.3rem;
}

/* Instructions Panel */
.instructions-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(118, 75, 162, 0.1);
}

.instructions-content h3 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.instructions-content h3 i {
    color: #764ba2;
    margin-left: 15px;
}

.game-description {
    background: linear-gradient(135deg, #764ba2, #667eea);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.game-description p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

.instruction-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-card {
    background: #f7fafc;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.instruction-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: #764ba2;
}

.instruction-card i {
    font-size: 2rem;
    color: #764ba2;
    margin-bottom: 10px;
}

.instruction-card h4 {
    color: #2d3748;
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.instruction-card p {
    color: #4a5568;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.level-selector {
    margin: 30px 0;
}

.level-selector h4 {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.level-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.level-btn {
    padding: 20px;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    background: white;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    min-width: 140px;
    text-align: center;
}

.level-btn:hover {
    border-color: #764ba2;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(118, 75, 162, 0.2);
}

.level-btn.active {
    background: linear-gradient(135deg, #764ba2, #667eea);
    color: white;
    border-color: #764ba2;
}

.level-btn i {
    font-size: 1.8rem;
}

.level-btn span {
    font-size: 1.1rem;
}

.level-btn small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.start-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 30px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(72, 187, 120, 0.4);
}

/* Game Area */
.word-game-area {
    background: white;
    border-radius: 25px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(118, 75, 162, 0.1);
}

.question-section {
    margin-bottom: 30px;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.question-image {
    font-size: 4rem;
    text-align: center;
}

.timer-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #4a5568;
    font-weight: 600;
}

.timer-progress {
    width: 200px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.timer-bar {
    height: 100%;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 4px;
    transition: width 0.1s ease;
}

.sentence-display {
    text-align: center;
    margin-bottom: 30px;
    background: #f7fafc;
    padding: 30px;
    border-radius: 20px;
    border: 3px solid #e2e8f0;
}

.sentence-display h3 {
    color: #2d3748;
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.4;
    margin: 0;
}

.hint-section {
    text-align: center;
    margin-bottom: 20px;
}

.hint-btn {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto 15px;
}

.hint-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(237, 137, 54, 0.4);
}

.hint-display {
    background: rgba(237, 137, 54, 0.1);
    border: 2px solid #ed8936;
    padding: 15px 20px;
    border-radius: 15px;
    color: #9c4221;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    max-width: 400px;
    margin: 0 auto;
    animation: slideDown 0.5s ease;
}

.hint-display i {
    font-size: 1.2rem;
}

.options-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
}

.word-option {
    padding: 20px;
    background: white;
    border: 3px solid #e2e8f0;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1.2rem;
    color: #2d3748;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.word-option:hover {
    border-color: #764ba2;
    background: rgba(118, 75, 162, 0.1);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.3);
}

/* Result Panel */
.result-panel {
    background: white;
    border-radius: 25px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(118, 75, 162, 0.1);
}

.result-content {
    max-width: 450px;
    margin: 0 auto;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.result-icon.success {
    color: #48bb78;
}

.result-icon.error {
    color: #f56565;
}

.result-icon.timeout {
    color: #ed8936;
}

.result-content h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

.result-content p {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.1rem;
    line-height: 1.5;
}

.correct-answer-display {
    background: #f7fafc;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    color: #764ba2;
    font-weight: 600;
    font-size: 1.1rem;
}

.correct-answer-display span:last-child {
    color: #2d3748;
    font-weight: 700;
}

.result-stats {
    background: rgba(118, 75, 162, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
    color: #764ba2;
    font-weight: 600;
    font-size: 1.1rem;
}

.next-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.next-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

/* Game Complete Panel */
.game-complete-panel {
    background: white;
    border-radius: 25px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(118, 75, 162, 0.1);
}

.complete-trophy {
    font-size: 6rem;
    color: #ffd700;
    margin-bottom: 25px;
    animation: bounce 2s infinite;
}

.complete-content h2 {
    color: #2d3748;
    font-size: 2.2rem;
    margin-bottom: 35px;
    font-weight: 700;
}

.final-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stat-card {
    background: #f7fafc;
    padding: 25px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    min-width: 150px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2.5rem;
    color: #764ba2;
}

.stat-details {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.language-assessment {
    background: #f7fafc;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 35px;
    border: 2px solid #e2e8f0;
}

.language-assessment h4 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600;
}

.assessment-result span {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 10px;
}

.assessment-result p {
    margin: 0;
    color: #4a5568;
    font-size: 1.1rem;
}

.complete-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #764ba2, #667eea);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-8px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .missing-word-container {
        padding: 15px;
    }
    
    .word-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
    }
    
    .instruction-cards {
        grid-template-columns: 1fr;
    }
    
    .level-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .level-btn {
        width: 100%;
        max-width: 250px;
    }
    
    .question-header {
        flex-direction: column;
        text-align: center;
    }
    
    .sentence-display h3 {
        font-size: 1.5rem;
    }
    
    .options-section {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .timer-progress {
        width: 150px;
    }
    
    .final-stats {
        flex-direction: column;
        align-items: center;
    }
    
    .complete-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .question-image {
        font-size: 3rem;
    }
    
    .sentence-display {
        padding: 20px;
    }
    
    .sentence-display h3 {
        font-size: 1.3rem;
    }
    
    .word-option {
        padding: 15px;
        font-size: 1rem;
        min-height: 60px;
    }
    
    .complete-trophy {
        font-size: 4rem;
    }
    
    .complete-content h2 {
        font-size: 1.8rem;
    }
    
    .timer-progress {
        width: 120px;
    }
}
